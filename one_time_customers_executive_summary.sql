-- Executive Summary: One-Time Customer Analysis
-- Key metrics and insights for business decision making

-- =====================================================
-- EXECUTIVE DASHBOARD METRICS
-- =====================================================

-- Overall One-Time Customer Metrics
SELECT 
    'EXECUTIVE SUMMARY' as report_section,
    'Overall Metrics' as metric_category,
    COUNT(DISTINCT c.customer_id) as total_one_time_customers,
    ROUND(AVG(o.current_total_price_set), 2) as avg_order_value,
    ROUND(SUM(o.current_total_price_set), 2) as total_revenue_from_one_timers,
    ROUND(AVG(o.current_total_discounts_set / NULLIF(o.current_subtotal_price_set, 0) * 100), 2) as avg_discount_percentage,
    COUNT(CASE WHEN o.discount_codes IS NOT NULL AND o.discount_codes != '' THEN 1 END) as customers_using_promo_codes,
    ROUND(COUNT(CASE WHEN o.discount_codes IS NOT NULL AND o.discount_codes != '' THEN 1 END) * 100.0 / COUNT(*), 2) as promo_code_usage_rate
FROM metrics.shopify_customers c
JOIN metrics.shopify_orders o ON c.shopify_id = o.customer_id
WHERE c.number_of_orders = 1

UNION ALL

-- Support Ticket Metrics
SELECT 
    'EXECUTIVE SUMMARY' as report_section,
    'Support Metrics' as metric_category,
    COUNT(DISTINCT co.customer_id) as customers_with_support_tickets,
    ROUND(COUNT(DISTINCT co.customer_id) * 100.0 / (
        SELECT COUNT(*) FROM metrics.shopify_customers WHERE number_of_orders = 1
    ), 2) as support_ticket_rate_pct,
    COUNT(DISTINCT cst.ticket_id) as total_support_tickets,
    ROUND(AVG(cst.resolution_time_hours), 1) as avg_resolution_hours,
    ROUND(AVG(cs.csat_score), 2) as avg_csat_score,
    COUNT(CASE WHEN cs.csat_score <= 2 THEN 1 END) as dissatisfied_customers
FROM (
    SELECT c.customer_id, c.shopify_id 
    FROM metrics.shopify_customers c 
    WHERE c.number_of_orders = 1
) otc
JOIN metrics.shopify_orders o ON otc.shopify_id = o.customer_id
JOIN customer_orders co ON o.order_id = co.order_id
LEFT JOIN customer_support_tickets cst ON co.customer_id = cst.customer_id
LEFT JOIN csat_surveys cs ON cst.ticket_id = cs.ticket_id;

-- =====================================================
-- KEY BUSINESS INSIGHTS
-- =====================================================

-- 1. Revenue Impact Analysis
SELECT 
    'BUSINESS INSIGHTS' as report_section,
    'Revenue Impact' as insight_category,
    'One-Time Customer Revenue vs Total' as metric_name,
    CONCAT(
        ROUND(
            (SELECT SUM(o.current_total_price_set) 
             FROM metrics.shopify_customers c 
             JOIN metrics.shopify_orders o ON c.shopify_id = o.customer_id 
             WHERE c.number_of_orders = 1) * 100.0 / 
            (SELECT SUM(current_total_price_set) FROM metrics.shopify_orders), 2
        ), '%'
    ) as value,
    'Percentage of total revenue from one-time customers' as description

UNION ALL

-- 2. Discount Dependency Analysis
SELECT 
    'BUSINESS INSIGHTS' as report_section,
    'Discount Dependency' as insight_category,
    'High Discount Customers' as metric_name,
    CONCAT(
        COUNT(CASE WHEN (o.current_total_discounts_set / NULLIF(o.current_subtotal_price_set, 0) * 100) > 25 THEN 1 END),
        ' customers (',
        ROUND(COUNT(CASE WHEN (o.current_total_discounts_set / NULLIF(o.current_subtotal_price_set, 0) * 100) > 25 THEN 1 END) * 100.0 / COUNT(*), 1),
        '%)'
    ) as value,
    'Customers who only bought with >25% discount' as description
FROM metrics.shopify_customers c
JOIN metrics.shopify_orders o ON c.shopify_id = o.customer_id
WHERE c.number_of_orders = 1

UNION ALL

-- 3. Support Issue Correlation
SELECT 
    'BUSINESS INSIGHTS' as report_section,
    'Support Correlation' as insight_category,
    'Post-Purchase Issues' as metric_name,
    CONCAT(
        COUNT(DISTINCT CASE WHEN cst.ticket_timing = 'Post-Purchase' THEN co.customer_id END),
        ' customers (',
        ROUND(COUNT(DISTINCT CASE WHEN cst.ticket_timing = 'Post-Purchase' THEN co.customer_id END) * 100.0 / COUNT(DISTINCT co.customer_id), 1),
        '%)'
    ) as value,
    'One-time customers who had post-purchase support issues' as description
FROM customer_orders co
LEFT JOIN customer_support_tickets cst ON co.customer_id = cst.customer_id;

-- =====================================================
-- ACTIONABLE RECOMMENDATIONS
-- =====================================================

-- Top Products Attracting One-Time Customers (Potential Issues)
SELECT 
    'RECOMMENDATIONS' as report_section,
    'Product Analysis' as recommendation_category,
    oli.sku_name as product_name,
    COUNT(DISTINCT co.customer_id) as one_time_customers,
    COUNT(DISTINCT CASE WHEN cst.ticket_id IS NOT NULL THEN co.customer_id END) as customers_with_issues,
    ROUND(COUNT(DISTINCT CASE WHEN cst.ticket_id IS NOT NULL THEN co.customer_id END) * 100.0 / COUNT(DISTINCT co.customer_id), 1) as issue_rate_pct,
    ROUND(AVG(co.order_total), 2) as avg_order_value,
    CASE 
        WHEN COUNT(DISTINCT CASE WHEN cst.ticket_id IS NOT NULL THEN co.customer_id END) * 100.0 / COUNT(DISTINCT co.customer_id) > 30 
        THEN 'HIGH RISK: Review product quality/description'
        WHEN COUNT(DISTINCT CASE WHEN cst.ticket_id IS NOT NULL THEN co.customer_id END) * 100.0 / COUNT(DISTINCT co.customer_id) > 15 
        THEN 'MEDIUM RISK: Monitor closely'
        ELSE 'LOW RISK: Performing well'
    END as recommendation
FROM customer_orders co
JOIN order_line_items oli ON co.customer_id = oli.customer_id AND co.order_id = oli.order_id
LEFT JOIN customer_support_tickets cst ON co.customer_id = cst.customer_id
GROUP BY oli.sku_name
HAVING COUNT(DISTINCT co.customer_id) >= 5  -- Only products with 5+ one-time customers
ORDER BY issue_rate_pct DESC, one_time_customers DESC
LIMIT 20;

-- =====================================================
-- OPERATIONAL INSIGHTS
-- =====================================================

-- Support Team Performance with One-Time Customers
SELECT 
    'OPERATIONAL INSIGHTS' as report_section,
    'Support Performance' as insight_category,
    cst.assignee_team,
    COUNT(DISTINCT cst.ticket_id) as tickets_handled,
    COUNT(DISTINCT cst.customer_id) as unique_customers,
    ROUND(AVG(cst.resolution_time_hours), 1) as avg_resolution_hours,
    ROUND(AVG(cs.csat_score), 2) as avg_csat_score,
    COUNT(CASE WHEN cs.csat_score <= 2 THEN 1 END) as dissatisfied_responses,
    CASE 
        WHEN AVG(cs.csat_score) >= 4 AND AVG(cst.resolution_time_hours) <= 24 THEN 'EXCELLENT'
        WHEN AVG(cs.csat_score) >= 3.5 AND AVG(cst.resolution_time_hours) <= 48 THEN 'GOOD'
        WHEN AVG(cs.csat_score) >= 3 THEN 'NEEDS IMPROVEMENT'
        ELSE 'CRITICAL - IMMEDIATE ACTION REQUIRED'
    END as performance_rating
FROM customer_support_tickets cst
LEFT JOIN csat_surveys cs ON cst.ticket_id = cs.ticket_id
WHERE cst.assignee_team IS NOT NULL
GROUP BY cst.assignee_team
ORDER BY avg_csat_score DESC, avg_resolution_hours ASC;

-- =====================================================
-- CHURN RISK INDICATORS
-- =====================================================

-- High-Risk One-Time Customer Segments
SELECT 
    'CHURN RISK ANALYSIS' as report_section,
    'Risk Segments' as analysis_category,
    CASE 
        WHEN co.discount_percentage > 30 AND cst.ticket_id IS NOT NULL AND cs.csat_score <= 2 
        THEN 'CRITICAL RISK: High discount + Support issues + Low satisfaction'
        WHEN co.discount_percentage > 25 AND cst.ticket_id IS NOT NULL 
        THEN 'HIGH RISK: High discount dependency + Support issues'
        WHEN cst.ticket_id IS NOT NULL AND cs.csat_score <= 2 
        THEN 'HIGH RISK: Support issues + Low satisfaction'
        WHEN co.discount_percentage > 30 
        THEN 'MEDIUM RISK: High discount dependency'
        WHEN cst.ticket_id IS NOT NULL 
        THEN 'MEDIUM RISK: Had support issues'
        ELSE 'LOW RISK: Clean purchase experience'
    END as risk_category,
    COUNT(DISTINCT co.customer_id) as customer_count,
    ROUND(COUNT(DISTINCT co.customer_id) * 100.0 / (
        SELECT COUNT(*) FROM metrics.shopify_customers WHERE number_of_orders = 1
    ), 2) as percentage_of_one_timers,
    ROUND(AVG(co.order_total), 2) as avg_order_value,
    ROUND(AVG(co.discount_percentage), 1) as avg_discount_pct,
    ROUND(AVG(cs.csat_score), 2) as avg_satisfaction_score
FROM customer_orders co
LEFT JOIN customer_support_tickets cst ON co.customer_id = cst.customer_id
LEFT JOIN csat_surveys cs ON cst.ticket_id = cs.ticket_id
GROUP BY 
    CASE 
        WHEN co.discount_percentage > 30 AND cst.ticket_id IS NOT NULL AND cs.csat_score <= 2 
        THEN 'CRITICAL RISK: High discount + Support issues + Low satisfaction'
        WHEN co.discount_percentage > 25 AND cst.ticket_id IS NOT NULL 
        THEN 'HIGH RISK: High discount dependency + Support issues'
        WHEN cst.ticket_id IS NOT NULL AND cs.csat_score <= 2 
        THEN 'HIGH RISK: Support issues + Low satisfaction'
        WHEN co.discount_percentage > 30 
        THEN 'MEDIUM RISK: High discount dependency'
        WHEN cst.ticket_id IS NOT NULL 
        THEN 'MEDIUM RISK: Had support issues'
        ELSE 'LOW RISK: Clean purchase experience'
    END
ORDER BY customer_count DESC;
