defmodule Storybook.Components.CoreComponents.Stat do
  use PhoenixStorybook.Story, :component

  def function, do: &AdminWeb.CoreComponents.stat/1
  def imports, do: [{AdminWeb.CoreComponents, button: 1}]
  def render_source, do: :function

  def apex_chart_template, do: AdminWeb.OutboundSMSBatchComponents.Show.total_chart(nil,50,75)

  # def template do
  #   """
  #   <div>
  #     <.button phx-click={IO.inspect("#:text", label: "Woah")}>
  #       Show data for #:variation_id
  #     </.button>
  #     <.psb-variation/>
  #   </div>
  #   """
  # end

  def pack(data) do
    data
    |> JSON.encode!()
    |> Base.encode64()
  end

  def layout_for(:apex_charts) do
    AdminWeb.OutboundSmsBatchLive.Components.Show.total_chart(nil, 34, 66)
    |> pack()
  end
  def data_for(:apex_charts), do: [34, 66] |> pack()

  def layout_for(:echarts) do
    %{
      title: %{
        text: "Automation",
        subtext: "Time Summary",
        left: "center"
      },
      tooltip: %{
        trigger: "item"
      },
      legend: %{
        # formatter: function (name, a) {
        #   return "Legend d " + name + " " + a;
        # },
        orient: "vertical",
        top: "center",
        left: "right"
      },
      series: [
        %{
          name: "Automation",
          type: "pie",
          radius: "50%",
          center: ["25%", "50%"],
          labelLine: false,
          data: [
            %{ value: 33, name: "Time 'saved'" },
            %{ value: 64, name: "Time spent debugging" },
            %{ value: 64, name: "Time spent debugging2" },
            %{ value: 64, name: "Time spent debugging3" },
            %{ value: 64, name: "Time spent debugging4" },
          ],
          emphasis: %{
            itemStyle: %{
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)"
            }
          }
        }
      ]
    }
    |> pack()
  end
  def data_for(:echarts), do: [34, 66] |> pack()

  def variations do
    [
      %Variation{
        id: :echarts,
        attributes: %{
          text: "ECharting about it",
          chart?: true
        },
        slots: [
          """
          <div
              id="totals-pie-echarts"
              phx-hook="EChartHook"
              phx-update="ignore"
              data-ref-id="pie-echarts-1234:id:idk"
              data-template="#{layout_for(:echarts)}"
              data-debug-data="#{data_for(:echarts)}"
              style="width: 350px; height: 450px"
            />
          """
        ]
      },
      %Variation{
        id: :apex_charts,
        attributes: %{
          text: "Apex Charting about it",
          chart?: true
        },
        slots: [
          """
          <div
              id="totals-pie-apex"
              phx-hook="ChartHook"
              phx-update="ignore"
              data-batch-id="pie-apex-1234:id:idk"
              data-template="#{layout_for(:apex_charts)}"
              data-debug-data="#{data_for(:apex_charts)}"
            />
          """
        ]
      },
      %VariationGroup{
        id: :colors,
        description: "Trends",
        variations: [
          %Variation{
            id: :trend_up,
            attributes: %{
              text: "Trending up",
              trend: "🤩",
              trend_up?: true,
            },
            slots: [
              """
              <:icon>
                <Heroicons.paper_airplane class="text-white h-6 w-6" />
              </:icon>
              1,234
              """
            ]
          },
          %Variation{
            id: :trend_down,
            attributes: %{
              text: "Crying about it",
              trend: "🙃",
              trend_up?: false
            },
            slots: [
              """
              <:icon>
                <Heroicons.paper_airplane class="text-white h-6 w-6" />
              </:icon>
              42
              """
            ]
          }
        ]
      }
    ]
  end
end
