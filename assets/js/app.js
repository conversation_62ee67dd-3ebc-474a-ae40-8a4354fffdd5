// We import the CSS which is extracted to its own file by esbuild.
// Remove this line if you add a your own CSS build pipeline (e.g postcss).

// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"

// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import "./components"
import topbar from "../vendor/topbar"
import "./user_socket.js"
import { Hotkeys } from "./hooks/hotkeys.js"
import { ChartHook } from "./hooks/chart_hook"
import { EChartHook } from "./hooks/echart_hook"
import { DownloadHandler } from "./hooks/download_hook"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

function debounce(func) {
    var timer;
    return function (event) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(func, 100, event);
    };
}

function applyTheme() {
    var theme = localStorage.getItem("theme") || "light";
    var wantsDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (theme === "system" && wantsDark) {
        document.documentElement.classList.add("dark");
        document.documentElement.style.setProperty("color-scheme", "dark");
        return;
    }
    if (theme === "system" && !wantsDark) {
        document.documentElement.classList.remove("dark");
        document.documentElement.style.setProperty("color-scheme", "light");
        return;
    }
    if (theme === "dark") {
        document.documentElement.classList.add("dark");
        document.documentElement.style.setProperty("color-scheme", "dark");
    } else {
        document.documentElement.classList.remove("dark");
        document.documentElement.style.setProperty("color-scheme", "light");
    }
}


// set listener to update color scheme preference on system preference change
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
    let theme = localStorage.getItem("theme");
    console.log(event.matches);
    console.log(theme);
    console.log(theme == "system" && event.matches)

    if (theme == "system" && event.matches) {
        console.log("Wants darkmode");
        applyTheme();
    } else {
        console.log("Wants lightmode");
        applyTheme();
    }
});

// check color scheme preference on page load
applyTheme();

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
let topBarScheduled = undefined;
window.addEventListener("phx:session-storage", (event) => {
    const { method, params } = event.detail;
    const result = localStorage[method](...params);
    if (method === "getItem") {
        liveSocket.main.channel.push("phx:session-storage-item", {
            key: params.at(0),
            value: result,
        });
    }
});

window.addEventListener("phx:set-theme", ({ detail: { theme: theme } }) => {
    localStorage.setItem("theme", theme);
    liveSocket.main.channel.push("phx:set-theme", { theme: theme })
    applyTheme();
});

window.addEventListener("phx:page-loading-start", () => {
    if (!topBarScheduled) {
        topBarScheduled = setTimeout(() => {
            topbar.show();
        }, 120);
    }
});
window.addEventListener("phx:page-loading-stop", () => {
    clearTimeout(topBarScheduled);
    topBarScheduled = undefined;
    topbar.hide();
});
window.addEventListener("phx:copy-text", ({ detail }) => {
    if ("clipboard" in navigator) {
        const text = detail.text;
        navigator.clipboard.writeText(text);
    } else {
        alert("Sorry, your browser does not support clipboard copy.");
    }
});
window.addEventListener("phx:copy", (event) => {
    let button = event.detail.dispatcher;
    let text = event.target.innerText;

    navigator.clipboard.writeText(text).then(() => {
        button.innerText = "Copied!";
        setTimeout(() => {
            button.innerText = "Copy";
        }, 2000);
    });
});
window.addEventListener("phx:copy-format", (event) => {
    let button = event.detail.dispatcher;
    let htmlContent = event.target.innerHTML;

    navigator.clipboard.write([
        new ClipboardItem({
            "text/html": new Blob([htmlContent], { type: "text/html" })
        })
    ]).then(() => {
        button.innerText = "Copied!";
        setTimeout(() => {
            button.innerText = "Copy";
        }, 2000);
    }).catch(err => {
        console.error("Failed to copy content: ", err);
    });
});
window.addEventListener("phx:copy-text-target", (event) => {
    if ("clipboard" in navigator) {
        const text = event.target.textContent;
        navigator.clipboard.writeText(text);
    } else {
        alert("Sorry, your browser does not support clipboard copy.");
    }
});

window.addEventListener("phx:live_reload:attached", ({ detail: reloader }) => {
    // Enable server log streaming to client.
    // Disable with reloader.disableServerLogs()
    reloader.enableServerLogs()
    window.liveReloader = reloader
});

// Custom event handlers for showing and hiding modals
window.addEventListener("phx:show_modal", (event) => {
    const modalId = event.detail.id;
    if (!modalId) return;

    // Show the modal container
    const modal = document.getElementById(modalId);
    if (modal) modal.classList.remove("hidden");

    // Show the background with transition
    const bg = document.getElementById(`${modalId}-bg`);
    if (bg) {
        bg.classList.remove("hidden");
        bg.classList.add("opacity-100");
        bg.classList.remove("opacity-0");
    }

    // Show the container with transition
    const container = document.getElementById(`${modalId}-container`);
    if (container) {
        container.classList.remove("hidden");
        container.classList.add("opacity-100", "translate-y-0", "sm:scale-100");
        container.classList.remove("opacity-0", "translate-y-4", "sm:translate-y-0", "sm:scale-95");
    }

    // Add overflow-hidden to body
    document.body.classList.add("overflow-hidden");

    // Focus first element
    setTimeout(() => {
        const content = document.getElementById(`${modalId}-content`);
        if (content) {
            const focusable = content.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (focusable) focusable.focus();
        }
    }, 50);
});

window.addEventListener("phx:hide_modal", (event) => {
    const modalId = event.detail.id;
    if (!modalId) return;

    // Hide the background with transition
    const bg = document.getElementById(`${modalId}-bg`);
    if (bg) {
        bg.classList.add("opacity-0");
        bg.classList.remove("opacity-100");
    }

    // Hide the container with transition
    const container = document.getElementById(`${modalId}-container`);
    if (container) {
        container.classList.add("opacity-0", "translate-y-4", "sm:translate-y-0", "sm:scale-95");
        container.classList.remove("opacity-100", "translate-y-0", "sm:scale-100");
    }

    // Hide the modal after transition
    setTimeout(() => {
        const modal = document.getElementById(modalId);
        if (modal) modal.classList.add("hidden");
        if (bg) bg.classList.add("hidden");
        if (container) container.classList.add("hidden");

        // Remove overflow-hidden from body
        document.body.classList.remove("overflow-hidden");
    }, 200);
});

// Event handler for js-exec events
window.addEventListener("phx:js-exec", (event) => {
    const { to, attr } = event.detail;
    if (!to) return;

    const el = document.querySelector(to);
    if (!el) return;

    if (attr === "data-cancel") {
        // Simulate clicking the cancel button or X button
        const closeButton = el.querySelector('button[aria-label="close"]');
        if (closeButton) closeButton.click();
    }
});

let Hooks = {}
Hooks.Hotkeys = Hotkeys;
Hooks.ChartHook = ChartHook;
Hooks.EChartHook = EChartHook;
Hooks.DownloadHandler = DownloadHandler;
// Hooks.VegaLite = VegaLite;

Hooks.LocalTime = {
    mounted() {
        this.el.innerText = new Date(this.el.getAttribute("datetime")).toLocaleString();
    },
    updated() {
        this.el.innerText = new Date(this.el.getAttribute("datetime")).toLocaleString();
    }
}

Hooks.AutoScale = {
    calculate_best_scale(el, width) {
        let min_scale = 0.2;
        let max_scale = 1.0;

        let { offsetWidth } = el;
        let width_scale = parseFloat((offsetWidth / width).toFixed(1));

        return Math.max(Math.min(width_scale, max_scale), min_scale);
    },
    send_scale_event(scale) {
        this.pushEvent("auto-scale", { scale })
    },
    debounce(func, wait, immediate) {
        var timeout;
        return function () {
            var context = this, args = arguments;
            var later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },
    mounted() {
        let { width } = this.el.dataset;
        let scale = this.calculate_best_scale(this.el, width);
        this.send_scale_event(scale);

        window.addEventListener("resize", this.debounce(() => {
            let scale = this.calculate_best_scale(this.el, width);
            this.send_scale_event(scale);
        }, 250));

        this.handleEvent("auto-scale", () => {
            let scale = this.calculate_best_scale(this.el, width);
            this.send_scale_event(scale);
        })
    }
}

Hooks.LightSwitch = {
    mounted() {

    }
}

Hooks.ScrollToNew = {
    mounted() {
        this.handleEvent("scroll-down", () => {
            this.el.scrollTop = this.el.scrollHeight;
        })
    }
}

let liveSocket = new LiveSocket("/live", Socket, {
    hooks: Hooks,
    params: { _csrf_token: csrfToken },
    dom: {
        onBeforeElUpdated(from, to) {
            if (from._x_dataStack) {
                window.Alpine.clone(from, to)
            }
        }
    },
    metadata: {
        click: (e, _el) => { return { offsetX: e.offsetX, offsetY: e.offsetY } }
    }
});

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket
