import { Formatters } from "../formatters.js";


async function makeChart(el) {
    if (!window.echarts) {
        const echartsModule = await import("../../vendor/echarts.js");
        // ECharts exports as default in most builds
        window.echarts = echartsModule.default || echartsModule;
    }

    let chart = window.echarts.init(el);
    return chart;
}

function replace_formatters(obj) {
    if (typeof obj === "string") {
        // Change the regex if you need a more permissive match.
        const match = obj.match(/^::([A-z]+)::$/);
        if (match && Formatters[match[1]]) {
            return Formatters[match[1]];
        }
        return obj;
    } else if (Array.isArray(obj)) {
        return obj.map(replace_formatters);
    } else if (obj && typeof obj === "object") {
        const newObj = {};
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                newObj[key] = replace_formatters(obj[key]);
            }
        }
        return newObj;
    }
    return obj;
}

export const EChartHook = {
    async mounted() {
        // Store the ref ID for comparison
        this.refId = this.el.dataset.refId;
        this.template = this.unpack(this.el.dataset.template)

        // Get the chart type from the element ID or ref ID
        this.chartType = this.el.id.split('-')[0] || this.refId?.split('-')[0] || 'chart';

        // Listen for chart events for this chart type and general chart updates
        this.handleEvent(`chart-${this.chartType}`, (options) => {
            this.updateOptions(this.el, options);
        });

        // Listen for general chart update events
        this.handleEvent("chart-update", (payload) => {
            if (payload.chart_id === this.el.id) {
                this.updateData(payload.data);
            }
        });

        console.log(`EChart hook mounted for ${this.chartType}, ref ID: ${this.refId}`);

        try {
            this.chart = await makeChart(this.el);
            window.patchart = this.chart;

            if (this.template) {
                this.chart.setOption(this.template);
            }

            // Add resize listener for responsive charts
            this.resizeObserver = new ResizeObserver(() => {
                if (this.chart) {
                    this.chart.resize();
                }
            });
            this.resizeObserver.observe(this.el);

        } catch (error) {
            console.error(`Error rendering initial chart ${this.chartType}:`, error);
        }
    },

    destroyed() {
        if (this.chart) {
            console.log(`Destroying chart: ${this.chartType}`);
            this.chart.destroy();
        }

        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
    },

    updated() {
        // Check if the ref ID has changed
        const newRefId = this.el.dataset.refId;
        if (this.refId !== newRefId) {
            console.log(`Ref ID changed from ${this.refId} to ${newRefId} for ${this.chartType}`);

            // Update the stored ref ID
            this.refId = newRefId;

            // Check if template has changed
            const newTemplate = this.unpack(this.el.dataset.template);
            if (JSON.stringify(this.template) !== JSON.stringify(newTemplate)) {
                this.template = newTemplate;
                if (this.chart && this.template) {
                    this.chart.setOption(this.template, true); // true for notMerge
                }
            }
        }
    },

    async updateOptions(el, options) {
        console.log(`Updating chart ${this.chartType} with new options`, options);

        // Store options in window for debugging
        window[this.el.id] = options;

        if (this.chart) {
            this.chart.setOption(options);
        }
    },

    updateData(newData) {
        if (!this.chart) return;

        console.log(`Updating chart ${this.chartType} with new data`, newData);

        // Update the chart with new data
        const option = {
            series: [{
                data: newData
            }]
        };

        this.chart.setOption(option);
    },

    unpack(template) {
        try {
            return JSON.parse(atob(template))
        } catch (e) {
            console.log(`An error occurred: ${e.toString()}`)
            return null;
        }
    }
};