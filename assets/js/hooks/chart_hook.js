import { Formatters } from "../formatters.js";


async function makeChart(el, chartOptions) {
    if (!window.ApexCharts) {
        const { default: ApexCharts } = await import("../../vendor/apexcharts.js");
        window.ApexCharts = ApexCharts;
    }

    window.opt = replace_formatters(chartOptions);
    window.chart_el = el;
    // opts = ... data = ...
    return new ApexCharts(el, chartOptions);
}

function replace_formatters(obj) {
    if (typeof obj === "string") {
        // Change the regex if you need a more permissive match.
        const match = obj.match(/^::([A-z]+)::$/);
        if (match && Formatters[match[1]]) {
            return Formatters[match[1]];
        }
        return obj;
    } else if (Array.isArray(obj)) {
        return obj.map(replace_formatters);
    } else if (obj && typeof obj === "object") {
        const newObj = {};
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                newObj[key] = replace_formatters(obj[key]);
            }
        }
        return newObj;
    }
    return obj;
}

export const ChartHook = {
    async mounted() {
        // Store the batch ID for comparison
        this.batchId = this.el.dataset.batchId;
        this.template = this.unpack(this.el.dataset.template)

        // Get the chart type from the element ID (totals or response_rate)
        this.chartType = this.el.id.split('-')[0];

        // Listen for chart events for this chart type
        this.handleEvent(`chart-${this.chartType}`, (options) => {
            this.updateOptions(this.el, options);
        });

        console.log(`Chart hook mounted for ${this.chartType}, batch ID: ${this.batchId}`);
        console.log(`Provided Template: ${this.template}`)

        // Create an empty chart initially to ensure the container is ready
        // This will be replaced when data arrives
        let initialOptions = {
            chart: {
                type: 'donut',
                width: 150,
                height: 150
            },
            series: [0],
            labels: ['Loading...']
        };

        if (this.template) {
            initialOptions = this.template;
        }

        try {
            this.chart = await makeChart(this.el, initialOptions);
            this.chart.render();
        } catch (error) {
            console.error(`Error rendering initial chart ${this.chartType}:`, error);
        }
    },

    destroyed() {
        if (this.chart) {
            console.log(`Destroying chart: ${this.chartType}`);
            this.chart.destroy();
        }
    },

    updated() {
        // Check if the batch ID has changed
        const newBatchId = this.el.dataset.batchId;
        if (this.batchId !== newBatchId) {
            console.log(`Batch ID changed from ${this.batchId} to ${newBatchId} for ${this.chartType}`);

            // Update the stored batch ID
            this.batchId = newBatchId;

            // If we have a chart, destroy it so it can be recreated with new data
            if (this.chart) {
                this.chart.destroy();
                this.chart = null;
            }
        }
    },

    async updateOptions(el, options) {
        console.log(`Updating chart ${this.chartType} with new options`, options);

        // Store options in window for debugging
        window[this.el.id] = options;

        // If chart exists, destroy it first to prevent memory leaks
        if (this.chart) {
            this.chart.destroy();
        }

        try {
            // Create and render the new chart
            this.chart = await makeChart(el, options);
            this.chart.render();
            console.log(`Chart ${this.chartType} rendered successfully`);
        } catch (error) {
            console.error(`Error rendering chart ${this.chartType}:`, error);
        }
    },
    unpack(template) {
        try {
            return JSON.parse(atob(template))
        } catch (e) {
            console.log(`An error occurred: ${e.toString()}`)
        }
    }
};