defmodule Dialer.ViciDialSchemas.CrmMapping do
  @moduledoc """
  This module provides the schema and common functions for the Mapping between the companies custom CRM and ViciDial.
  """
  use Ecto.Schema
  import Ecto.Changeset

  import Ecto.Query
  require Ecto.Query, warn: false

  @type t :: %__MODULE__{}

  @primary_key false
  schema "CrmMapping" do
    field :id, :integer, primary_key: true, source: :MappingID
    field :campaign_id, :string
    field :project_id, :integer, source: :projectID
    field :effort_id, :integer, source: :EffortID
    field :company_id, :integer, source: :CompanyID
  end

  def changeset(%__MODULE__{} = schema, attrs) do
    schema
    |> cast(attrs, [:campaign_id, :project_id, :effort_id, :company_id])
    |> validate_required([:campaign_id, :project_id])
    |> unique_constraint(:campaign_id)
    |> unique_constraint(:project_id, name: "projectid")
  end

  @doc """
  Inserts a mapping record into the database.

  This is required for efficient live reporting.

  Expects the first argument to be the dialer repo:
  `Dialer.Landline.Repo` or `Dialer.Wireless.Repo`

  Provide the new campaign_id you would like to associate to a Parent `project_id`.

  Returns an `:ok` tuple with the newly inserted record.
  """
  def insert_mapping(dialer_repo, campaign_id, project_id) do
    try do
      ret = insert_mapping!(dialer_repo, campaign_id, project_id)
      {:ok, ret}
    catch
      e -> {:error, e}
    end
  end

  @doc """
  See `insert_mapping/3` for more information.

  This function will raise an error if the record cannot be inserted.
  """
  def insert_mapping!(dialer_repo, campaign_id, project_id) do
    {effort_id, company_id} = fetch_effort_and_company_ids(project_id)

    %__MODULE__{}
    |> changeset(%{
      campaign_id: campaign_id,
      project_id: project_id,
      effort_id: effort_id,
      company_id: company_id
    })
    |> dialer_repo.insert!()
  end

  defp fetch_effort_and_company_ids(project_id) do
    query =
      from(epp in "effortprojects",
        join: e in "efforts",
        on: epp."EffortID" == e."EffortID",
        join: cc in "companycontacts",
        on: e."CompanyContactID" == cc."CompanyContactID",
        where: epp."ProjectID" == ^project_id,
        select: {e."EffortID", cc."CompanyID"}
      )

    case Crm.Repo.one(query) do
      {:ok, %{rows: [[effort_id, company_id]]}} -> {effort_id, company_id}
      {effort_id, company_id} -> {effort_id, company_id}
      _ -> {nil, nil}
    end
  end

  def _remove_crmMapping(campaign_id, repo) do
    repo.delete_all(from c in "CrmMapping", where: c.campaign_id == ^campaign_id)
  end
end
