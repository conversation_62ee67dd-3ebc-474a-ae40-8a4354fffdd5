defmodule Admin.Jobs.GorgiasShopifyLookupBackfiller do
  @moduledoc """
  Backfiller job that seeks through all Gorgias tickets where shopify_customer_id_looked_up is false,
  and then populates shopify_order_name, shopify_customer_id, shopify_order_id, and
  shopify_customer_id_looked_up=true by looking up the ticket directly via Gorgias API.
  """
  use Admin.Jobs.RecursiveJob, queue: :backfiller


  alias Admin.Integrations.Gorgias
  alias Admin.Integrations.Gorgias.{Tenant, Ticket, RestAPI}
  alias Admin.Integrations.Shopify.{Customer, Order}

  require Logger
  require Ash.Query

  @shopify_integration_id :"34482"

  @impl Admin.Jobs.RecursiveJob
  def fetch_batch(%{limit: limit}) do
    # Get all tenants
    tenants =
      Tenant
      |> Ash.read!()

    # For each tenant, find tickets where shopify_customer_id_looked_up is false
    tenants
    |> Enum.flat_map(fn tenant ->
      query =
        Ticket
        |> Ash.Query.filter(shopify_customer_id_looked_up == false)
        |> Ash.Query.limit(limit)

      case Ash.read(query, tenant: tenant) do
        {:ok, tickets} ->
          Enum.map(tickets, &{&1.id, tenant})
        {:error, error} ->
          Logger.error("Failed to fetch tickets for tenant #{tenant}: #{inspect(error)}")
          []
      end
    end)
    # May be forward-heavy, selecting up to the limit from only one tenant.
    |> Enum.take(limit)
  end

  @impl Admin.Jobs.RecursiveJob
  def perform_batch(_job, _options, batch) do
    Logger.info("Processing #{length(batch)} tickets for Shopify customer lookup")

    batch
    |> Enum.each(fn {ticket_id, tenant} ->
      process_ticket(ticket_id, tenant)
    end)

    :ok
  # rescue
  #   error ->
  #     Logger.error("Error in GorgiasShopifyLookupBackfiller: #{inspect(error)}")
  #     {:error, error}
  end

  defp process_ticket(ticket_id, tenant) do
    Logger.debug("Processing ticket #{ticket_id} for tenant #{tenant.id}")

    with {:ok, tenant} <- get_tenant(tenant),
         {:ok, %Tesla.Env{status: 200, body: ticket_data}} <- RestAPI.ticket(tenant, ticket_id),
         {:ok, shopify_data} <- extract_shopify_data(ticket_data),
         {:ok, _updated_ticket} <- update_ticket(ticket_id, tenant, shopify_data) do
      Logger.debug("Successfully updated ticket #{ticket_id} with Shopify data")
      :ok
    else
      {:ok, %Tesla.Env{status: status}} ->
        Logger.warning("Gorgias API returned status #{status} for ticket #{ticket_id}")
        mark_as_looked_up(ticket_id, tenant)

      {:error, reason} ->
        Logger.error("Failed to process ticket #{ticket_id}: #{inspect(reason)}")
        # Mark as looked up even if failed to avoid infinite retry
        mark_as_looked_up(ticket_id, tenant)

      _ ->
        Logger.error("Unexpected response for ticket #{ticket_id}")
        mark_as_looked_up(ticket_id, tenant)
    end
  end

  defp get_tenant(tenant) do
    case Ash.get(Tenant, tenant.id) do
      {:ok, tenant} -> {:ok, tenant}
      {:error, error} -> {:error, "Failed to get tenant: #{inspect(error)}"}
    end
  end

  defp extract_shopify_data(ticket_data) do
    shopify_customer_id =
      ticket_data
      |> get_in([:customer, :integrations, @shopify_integration_id, :customer, :id])
      |> dbg()

    shopify_data = %{
      shopify_customer_id: shopify_customer_id,
      # A ticket can have multiple orders; this won't work lol
      ### NOTE: When pulling a ticket, all orders are always pulled.
      ### Put another way, there is not a direct relation between ticket
      ### and specific order without taking time windows into consideration.
      ### (falls apart for busy customers)
      #### Custom field?
      # shopify_order_id: get_shopify_order_id(shopify),
      # shopify_order_name: get_shopify_order_name(shopify),
      shopify_customer_id_looked_up: true
    }

    {:ok, shopify_data}
  rescue
    error ->
      Logger.error("Error extracting Shopify data: #{inspect(error)}")
      # Return empty data but mark as looked up
      {:ok, %{shopify_customer_id_looked_up: true}}
  end

  defp update_ticket(ticket_id, tenant, shopify_data) do
    Ticket
    |> Ash.get!(ticket_id, tenant: tenant)
    |> Ash.Changeset.for_update(:update, shopify_data)
    |> Ash.update(tenant: tenant)
  end

  defp mark_as_looked_up(ticket_id, tenant) do
    case update_ticket(ticket_id, tenant, %{shopify_customer_id_looked_up: true}) do
      {:ok, _} ->
        Logger.debug("Marked ticket #{ticket_id} as looked up")
        :ok
      {:error, error} ->
        Logger.error("Failed to mark ticket #{ticket_id} as looked up: #{inspect(error)}")
        :error
    end
  end
end
