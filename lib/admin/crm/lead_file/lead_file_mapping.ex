defmodule Admin.Crm.LeadFile.LeadFileMapping do
  @moduledoc """
  This module represents a lead file mapping.

  It is used to map the columns of a lead file to the columns of a lead.
  """
  use Ash.Resource,
    domain: Admin.Crm.Domain,
    data_layer: AshPostgres.DataLayer,
    extensions: [
      AshAdmin.Resource
    ]

  alias Admin.Crm.LeadFile
  require Ecto.Query
  import Ecto.Query

  postgres do
    table "lead_file_mapping"
    repo Admin.AdminRepo
  end

  identities do
    # identity of []

    identity :heading_incoming_lfid, [:heading_incoming, :lead_file_id]
  end

  attributes do
    uuid_primary_key :id

    attribute :heading_incoming, :string do
      public? true
    end

    attribute :heading_mapped_to, :string do
      public? true
    end

    timestamps()
  end

  relationships do
    belongs_to :lead_file, LeadFile do
      public? true
      primary_key? true
      attribute_type :uuid
      attribute_writable? true
      allow_nil? false
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    # inserts and updates record if it exists
    create :upsert do
      accept :*
      upsert? true
      upsert_identity :heading_incoming_lfid
    end
  end

  def delete_lead_file_mappings_by_lf_id(lead_file_id) do
    records =
      Admin.AdminRepo.Replica.all(
        from lfm in Admin.Crm.LeadFile.LeadFileMapping,
          where: lfm.lead_file_id == ^lead_file_id
      )

    # Delete each record
    Enum.each(records, fn record ->
      changeset = Ash.Changeset.for_destroy(record, :destroy)
      Ash.destroy(changeset)
    end)
  end
end
