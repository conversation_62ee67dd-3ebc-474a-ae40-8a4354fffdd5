defmodule Admin.Crm.LeadFile.LeadFileProcessor do
  @moduledoc """
  This module is responsible for the prep-work to load leads into the system for processing.

  To do so, it performs these actions:

  1. Download & Open the file
  1. Stage leads
    1. if already staged: skip
    1. if partially staged (failure to stage), existing leads are de-staged for safety.
  1. Start DNCScrub process
    1. if a scrub exists: download it
    1. new scrubs are performed over SFTP and retained in S3
  1. Interpret DNCScrub results
  1. Aggregate the results and store them on the LeadFile
  1. Execute each unfinished task for the lead file; loads, or operations.

  While performing these tasks, it publishes status updates
  to the Admin.PubSub on the `setups:process:\#{id}` topic.

  Additionally, subscribers of that topic may also want to subscribe to the pub_sub topic in LeadFiles for each lead file.
  This includes all actions that are of the
  `update` type. E.g. "update", "stage", "mark_errored", etc. When registering/subscribing to the topic
  you will have to define handle_info("<name>", socket) for each action defined, and a catch all messages you want
  to ignore. Use Admin.PubSubNotifier.subscribe/2 to subscribe to the actions you want to listen.

  Example:
  Admin.PubSubNotifier.subscribe(LeadFile, "update")

  Job Options:

  - `testing?` :: **boolean** if true, some functionalities are disabled (such as API polling)
  defaults to false, only available in dev

  """
  use Oban.Worker,
    queue: :crm,
    unique: [
      period: :infinity,
      states: Oban.Job.states() -- ~w(discarded executing)a
    ]

  require Logger

  alias Admin.Crm.ContactEngine.LeadFileReportBuilder
  alias Admin.Crm.Errors.SetupError
  alias Admin.Crm.LeadFile.{LeadFileProcessorOutputLogger, LoadRecordProcessor}
  alias Admin.Crm.Setups.Setup
  alias Admin.Crm.{ContactEngine, List, Files, LeadFile}
  alias Admin.Integrations.DNCScrub
  alias Admin.Integrations.DNCScrub.{ScrubResult, ZipHelper}
  alias Admin.Integrations.RND
  alias Phoenix.PubSub

  # We previously scrubbed under a different campaign
  # This caused us to double our scrub count and run up against
  # our licenced scrub count.
  @nn_project_id "40837"
  @nn_upload_path "/~#{@nn_project_id}/_Upload"
  @nn_download_path "/~#{@nn_project_id}/_Output"

  def topic(%Setup{} = setup), do: "setups:process:#{setup.id || setup}"

  def subscribe(setup) do
    Phoenix.PubSub.subscribe(Admin.PubSub, topic(setup))
  end

  defp update_users(setup, job_id, message) do
    PubSub.broadcast(
      Admin.PubSub,
      topic(setup),
      {:lead_file_processor, job_id, message}
    )
  end

  defp update_stages(setup_topic, stage, leadfile) do
    # stage = String.to_atom(stage)

    PubSub.broadcast(
      Admin.PubSub,
      setup_topic,
      {:state_update, stage, leadfile}
    )
  end

  defp notify_users_of_completion(setup, job_id) do
    PubSub.broadcast(Admin.PubSub, topic(setup), {:lead_file_processor, job_id, :completed})
  end

  # test
  def send_broadcast(setup, data) do
    PubSub.broadcast(Admin.PubSub, topic(setup), data)
  end

  # test
  # def handle_info(%{group_prompt_response: value}) do
  #   # Process the response value received from the frontend
  #   Logger.debug("Received Response:: #{inspect(value)}")
  #   {:noreply}
  # end

  @impl Oban.Worker
  def timeout(_job), do: :timer.hours(1)

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"setup" => setup_id, "lead_file" => lead_file_id}} = job)
      when is_binary(setup_id) and is_binary(lead_file_id) do
    :erlang.system_flag(:backtrace_depth, 35)
    # Initial setup, it's not complicated so we'll use a with
    with {:ok, _pid} <- LeadFileProcessorOutputLogger.start_link(job.id, lead_file_id),
         {:ok, setup} <- Ash.get(Setup, setup_id),
         {:ok, lead_file} <- Ash.get(LeadFile, lead_file_id, load: [:loads, :operations]),
         :ok <- update_users(setup, job.id, "JOB START ##{job.id} =========="),
         :ok <- process_lead_file(job, setup, lead_file),
         :ok <- notify_users_of_completion(setup, job.id) do
      :ok
    else
      ret ->
        LeadFileProcessorOutputLogger.flush_logs_with_exception(
          job.id,
          "Unexpected result #{inspect(ret)}"
        )

        LeadFileProcessorOutputLogger.stop(job.id)

        {:current_stacktrace, stacktrace} = Process.info(self(), :current_stacktrace)

        Appsignal.send_error(
          SetupError.exception("Error during LeadFileProcessor.perform/1 with clause."),
          stacktrace,
          fn span ->
            Appsignal.Span.set_attribute(span, "setup", setup_id)
            Appsignal.Span.set_attribute(span, "lead_file", lead_file_id)
            Appsignal.Span.set_attribute(span, "failure_data", inspect(ret))
          end
        )

        {:error, "Error during LeadFileProcessor.perform/1 with clause."}
    end
  end

  @impl Oban.Worker
  def perform(%Oban.Job{} = _job) do
    {:cancel, "Missing setup or lead_file ids."}
  end

  def update_file(%List{file: %LeadFile{} = file} = list, action, params \\ %{}) do
    file =
      file
      |> Ash.Changeset.for_update(action, params)
      |> Ash.update!()

    {:ok, setup} =
      Setup
      |> Ash.get(file.setup_id)

    stage_step =
      %{
        staged: 1,
        scrubbing: 2,
        scrubbed: 3,
        analyzing: 4,
        analyzed: 5,
        held: 6,
        resuming: 7,
        loading: 8,
        loaded: 9,
        archived: 10
      }

    case action do
      :stage ->
        Logger.debug("Skip set up state update for staged")

      _ ->
        if Map.get(stage_step, file.state) > Map.get(stage_step, setup.state) do
          setup
          |> Ash.Changeset.for_update(action, %{})
          |> Ash.update!()
        else
          Logger.debug("Skip set up state update overall")
        end
    end

    update_stages("setups:process:#{file.setup_id}", file.state, file)

    Logger.debug("file state: #{file.state}")
    Logger.debug("Setup state: #{setup.state}")

    %List{
      list
      | file: file
    }
  end

  @doc """
  The meat & potatos of the worker.
  """
  defp process_lead_file(%{id: job_id} = job, %Setup{} = setup, %LeadFile{} = lead_file) do
    # This is all quite complicated and error prone, so we'll wrap it with a try and else
    # for the catchable things, and a rescue for errors
    # TODO: Make this notify_fn, or update_users function retain the output for later review.
    notify_fn = fn msg ->
      LeadFileProcessorOutputLogger.log_message(job_id, msg)
      update_users(setup, job_id, msg)
    end

    notify_fn.("Processing LeadFile #{lead_file.name}")

    # Load file into memory and collect metrics
    try do
      with {:ok, loaded_lead_file, data} <- Files.load_lead_file(lead_file, %{}) do
        # Let's avoid testing this kind of loads in prod.
        # test = List.new(lead_file, data)
        # Logger.debug("dataframe from new(): #{inspect(test.df)}")

        lead_file
        |> List.new(data)
        |> pre_process(notify_fn)
        # 5 Phone Clean
        |> List.gather_metrics(notify_fn)
        |> notify_phone_metrics(notify_fn)
        |> stage_leads(notify_fn)
        |> perform_dncscrubs(setup.dial_policy, notify_fn, job.args)
        |> interpret_dncscrub(setup.dial_policy, notify_fn)
        |> perform_rnd_process(setup, notify_fn)
        |> report_dncscrub(setup,notify_fn)
        |> process_lead_file_tasks(setup, lead_file, notify_fn)

        notify_fn.("Processing complete.")
        LeadFileProcessorOutputLogger.flush_logs(job_id)
        :ok
      else
        {:error, "Unable to locate file", {:http_error, 404, _e}} ->
          notify_fn.("ERROR: Could not locate file in lead file files. Please re-upload.")
          mark_missing_file(lead_file, %{"aws_s3_error" => "#{lead_file.url} not found"})
          {:error, "Unable to locate file"}
      end
    rescue
      e ->
        notify_fn.("⚠️👾 BUG DETECTED 👾⚠️")
        notify_fn.(inspect(e, label: "Unknown Error", pretty: true))
        notify_fn.(inspect(__STACKTRACE__, label: "Stacktrace", pretty: true))

        LeadFileProcessorOutputLogger.flush_logs_with_exception(job_id, e)

        notify_users_of_completion(setup, job_id)
        Logger.error(Exception.format(:error, e, __STACKTRACE__))
        {:error, "Unknown Error in setup processing"}
    end
  end

  defp mark_missing_file(%LeadFile{} = lead_file, error) do
    lead_file
    |> Ash.Changeset.for_update(:mark_errored, %{"error" => error})
    |> Ash.update!()
  end

  defp notify_phone_metrics(list, notify_fn) do
    notify_fn.(
      "File Loaded, phone metrics calculated.\nIdentified #{Keyword.get(list.metrics, :count)} phone numbers. Phone Fields:"
    )

    Logger.debug("Phone Metrics: #{inspect(Keyword.get(list.metrics, :phone_metrics))}")

    list.metrics
    |> Keyword.get(:phone_metrics)
    |> Enum.map(fn phone_metrics ->
      # TODO: Add ContactEngine state calling / sms removal metrics
      case phone_metrics do
        %{field: field, ratio: ratio, distinct: distinct, blank: blank} ->
          notify_fn.(
            "#{field}: #{distinct} unique #{ratio}/1 duplicate ratio #{blank} blank/invalid"
          )

        %{special_load_states: special_load_states} ->
          set_preprocess_notes(list.file.loads, :special_load_state_removals, special_load_states)

        %{special_sms_states: special_sms_states} ->
          set_preprocess_notes(list.file.loads, :special_sms_state_removals, special_sms_states)

        %{invalid_phones: invalid_phones} ->
          set_preprocess_notes(list.file.loads, :null_invalid_phones, invalid_phones)

        %{invalid_states: invalid_states} ->
          set_preprocess_notes(list.file.loads, :invalid_state_removals, invalid_states)
      end

      # notify_fn.("#{field}: #{distinct} unique #{ratio}/1 duplicate ratio #{blank} blank/invalid")
    end)

    list
  end

  # TODO: Add ContactEngine metrics into notes
  defp set_preprocess_notes(loads, pre, count) do
    [load] = loads

    valid_keys = [
      :invalid_state_removals,
      :null_invalid_phones,
      :special_load_state_removals,
      :special_sms_state_removals
    ]

    if pre in valid_keys do
      load
      |> Ash.Changeset.for_update(:set_preprocess_notes, %{pre => count})
      |> Ash.update!()
    else
      raise ArgumentError, "Invalid key: #{inspect(pre)}. Must be one of #{inspect(valid_keys)}"
    end
  end

  # MARK - Pre-Processing
  @doc """
  Pre-process the list of leads before staging them.

  #File Pre-Processing
      # 1 Mapped Fields Heading Replacement
      # 2 Date Field Conversion (Excel serial dates to YYYY-MM-DD)
      # 3 State Abbrev Replacements
      # 4 Invalid State Removal
      # 5 Row Removal - null + invalid phones
      # 6 homepostcode clean/padding
      # 7 Special State Removals
  """
  defp pre_process(list, notify_fn) do
    list
    |> List.replace_mapped_headings(notify_fn)
    |> List.convert_date_fields(notify_fn)
    |> List.list_state_replacements(notify_fn)
    |> ContactEngine.Helpers.pre_process()
    |> List.list_row_removals(notify_fn)
    |> List.clean_homepostcode(notify_fn)
    # TODO: Add ContactEngine special state removals based on NPA???
    |> List.special_state_removals(notify_fn)
  end

  # TODO: Add ContactEngine Metadata
  defp stage_leads(%List{file: %LeadFile{state: :new}} = list, notify_fn) do
    notify_fn.(
      "Loading #{inspect(list.rows)} leads into staging area (this can take multiple minutes)"
    )

    # Remove existing leads, in case we are re-running the job from a failure.
    # If there are none, this will be near-instant.
    :ok = List.destage_contacts(list)

    # Actually stage the leads
    :ok = List.stage_lead_file(list)

    list = update_file(list, :stage)
    # wait 5 secs before close func for update to apply

    list
  end

  defp stage_leads(list, notify_fn) do
    notify_fn.("LEAD FILE ALREADY STAGED. Skipping step: stage_leads")
    list
  end

  defp perform_dncscrub(list, dial_policy, notify_fn, job_args \\ %{})

  defp perform_dncscrub(list, dial_policy, notify_fn, %{"testing?" => exists}) do
    notify_fn.("JOB ARGS: testing? => #{inspect(exists)}\nSkipping DNC Scrub")
    list
  end

  defp perform_dncscrubs(
         %List{file: %LeadFile{scrub_result_url: path}} = list,
         dial_policy,
         notify_fn,
         _args
       )
       when not is_nil(path) do
    list =
      if list.file.state == :staged do
        list = update_file(list, :start_scrubbing)
        list = update_file(list, :stop_scrubbing)

        list
      else
        list
      end

    notify_fn.("DNC Scrub already performed on this file")

    list
  end

  defp perform_dncscrubs(%List{} = list, dial_policy, notify_fn, _job_args)
       when list.file.state == :staged or list.file.state == :scrubbing do
    Logger.debug("Dial Policy in scrub: #{dial_policy.template}")

    list = update_file(list, :start_scrubbing)

    {:ok, scrub_file} = ZipHelper.build_scrub_file(list, dial_policy)

    notify_fn.("DNC Scrub file built")

    :ok = DNCScrub.scrub_file("#{list.file.id}.csv", scrub_file)
    notify_fn.("DNC Scrub file uploaded")

    # Request that the API start working on our upload
    notify_fn.("Polling DNC Scrub API (can take up to 30 seconds)")

    # TODO: Optimize this out into a one-poll-per-setup stance
    :ok = DNCScrub.poll_sftp()
    notify_fn.("DNC Scrub API Polled")
    notify_fn.("Downloading and parsing the DNC results.")

    # pretend we already refactored below code
    # build nn file
    # :ok = DNCScrub.scrub_file("#{list.file.id}.csv", scrub_file, "/NNWR/_Upload")

    {:ok, scrub_results, scrub_url} = DNCScrub.download_and_parse_results(list.file.id)

    notify_fn.("Success. Imported results to local DNC Registry.")

    dial_policy_str = Atom.to_string(dial_policy.template)

    # Apply dbg( restrictions that are immediately available
    notify_fn.(
      "-------------- APPLYING DIAL POLICY: Wireless / VoIP --------------\n EBR?: #{inspect(dial_policy.ebr?)} B2B?: #{inspect(dial_policy.b2b?)}"
    )

    :ok =
      ContactEngine.Helpers.apply_first_pass_policy(
        list.file.id,
        dial_policy.ebr?,
        dial_policy.b2b?
      )

    notify_fn.("Done. Applied first pass policy.")

    if not dial_policy.ebr? do
      notify_fn.("-------------- PERFORMING SECOND SCRUB: NN PASS --------------")

      perform_dncscrub_nn(list, dial_policy, notify_fn)
    end

    list =
      update_file(list, :stop_scrubbing, %{
        "scrub_result_url" => scrub_url,
        "scrub_result_url_migrated?" => true
      })

    # # Updates contact_staging fields: *_dnc, *_line_type, *_count and creates source metrics.
    # nn? = (dial_policy == "nn" && true) || false

    # list = update_file(list, :start_analyzing)

    # notify_fn.("Applying DNC results to contact_staging and building metrics.")

    # DNCScrub.update_dnc_core(list.file.id, nn?)

    # notify_fn.("Success. Updated contact_staging and metrics.")

    # list = update_file(list, :stop_analyzing)

    list
  end

  def perform_dncscrub_nn(list, dial_policy, notify_fn) do
    # nn_file_unused = update_file(nn_file, :start_scrubbing)
    {:ok, scrub_file} = ZipHelper.build_second_scrub_file(list, dial_policy)

    notify_fn.("DNC Scrub file built")

    :ok = DNCScrub.scrub_file("#{list.file.id <> "_nn"}.csv", scrub_file, @nn_upload_path)
    notify_fn.("DNC Scrub file uploaded")

    # Request that the API start working on our upload
    notify_fn.("Polling DNC Scrub API (can take up to 30 seconds)")

    # TODO: Optimize this out into a one-poll-per-setup stance
    :ok = DNCScrub.poll_sftp()
    notify_fn.("DNC Scrub API Polled")
    notify_fn.("Downloading and parsing the NN DNC results.")

    {:ok, scrub_results, scrub_url} =
      DNCScrub.download_and_parse_results(false, list.file.id <> "_nn", @nn_download_path)

    # nn_file_unused =
    # update_file(nn_file, :stop_scrubbing, %{
    #  "scrub_result_url" => scrub_url #possibly overwriting initial lead file scrub url
    # })

    notify_fn.("Success. Updated result_code_nn to local DNC Registry.")
  end

  def interpret_dncscrub(%List{} = list, dial_policy, notify_fn)
      when list.file.state in [:scrubbed, :analyzing] do
    # %List{file: %LeadFile{} = file} = list

    # 2. Interpret
    #   a. (Param based query) Join contact_staging to dnc_registry on phone number
    #   b. Append where claue for each type of removal filter (DNC, Invalid, Malformed)
    #     - Consider dial policy
    #     - `join dnc_registry dnc on cs.homephone = dnc.phone_number where dnc.result_code in ('L','I','M')`
    #     - `set homephone = null on results`
    #     - loop over phone fields
    #

    # OLD:
    # Number of leads with wireless or voip as first number
    # Number of leads with landline as first number
    # NEW:
    # Number of contacts with at least one wireless or voip number
    # Number of contacts with no wireless voip numbers

    # Updates contact_staging fields: *_dnc, *_line_type, *_count and creates source metrics.
    # nn? = dial_policy.template == :nn

    notify_fn.("Interpreting DNC results to contact_staging and building metrics.")

    list =
      case list.file.state do
        :analyzing ->
          notify_fn.("Skipping start analyzing update")

          list

        :scrubbed ->
          update_file(list, :start_analyzing)
      end

    # DNCScrub.update_dnc_core(list.file.id, nn?)
    ContactEngine.Helpers.apply_dnc_results(list, dial_policy)


    list
  end

  def interpret_dncscrub(%List{file: %LeadFile{state: state}} = list, _dial_policy, notify_fn)
      when state in [:analyzed, :loading, :resuming] do
    notify_fn.("DNC results already interpreted on this file. Metrics applied.")

    list
  end

  def perform_rnd_process(list, %Setup{do_rnd?: false}, notify_fn) do
    notify_fn.("RND Process not configured. Skipping Reassignment DB lookup.")

    list
  end

  def perform_rnd_process(list, _setup, notify_fn) do

    #list = update_file(list, :start_rnd)

    # Gather records
    # - identify phones that are wireless
    # - keep one unique number
    # - calculate lookup date on a per-contact basis
    lookup_file = RND.Helpers.build_scrub_file(list)

    notify_fn.("RND file built")

    # Upload to SFTP
    # - Poll SFTP for results
    :ok = RND.upload_rnd_file("#{list.file.id}.csv", lookup_file)
    notify_fn.("RND lookup file uploaded")

    # Request that the API start working on our upload
    notify_fn.("Polling RND SFTP (can take up to 30 seconds)")

    # TODO: Optimize this out into a one-poll-per-setup stance
    :ok = RND.poll_sftp(list.file.id)

    notify_fn.("RND SFTP Polled")

    notify_fn.("Downloading and parsing the RND results.")
    # Download Results
    # Parse Results
    # Update phone meta-data
    RND.download_and_interpret_rnd_results(list, list.file.id)

    notify_fn.("Success. Updated contact_staging and metadata.")

    #list = update_file(list, :end_rnd) #needs status adds


    list
  end


  def report_dncscrub(list, setup, notify_fn) do
    notify_fn.("Updating source metrics...")

    # Update source metrics
    lead_file_id = DNCScrub.string_to_uuid(list.file.id)
    DNCScrub.fetch_leadfile_sourcemetrics(:homephone, lead_file_id, not setup.dial_policy.ebr?)

    notify_fn.("Building Lead File Report, this may take a while on large lists.")

    LeadFileReportBuilder.build_report(list.file.id)

    notify_fn.("Success. Updated contact_staging and metrics.")

    list = update_file(list, :stop_analyzing)

    list
  end

  defp process_lead_file_tasks(
         %List{} = list,
         %Setup{auto_load?: false} = setup,
         %LeadFile{state: state} = lead_file,
         notify_fn
       )
       when state != :resuming do
    notify_fn.("Setup is set to hold for review. Not processing loads.")
    list = update_file(list, :hold)

    list
  end

  defp process_lead_file_tasks(
         %List{} = list,
         %Setup{} = setup,
         %LeadFile{} = lead_file,
         notify_fn
       ) do
    if list.file.state == :resuming do
      notify_fn.("Resuming processing of held file.")
    end

    unfinished_loads =
      lead_file.loads
      |> Enum.filter(&(not &1.loaded?))
      |> Enum.map(&LoadRecordProcessor.new!(lead_file, &1))

    notify_fn.("Processing #{length(unfinished_loads)} loads")

    list =
      cond do
        list.file.state == :loading ->
          notify_fn.("Skipping start loading update")

          list

        list.file.state in [:resuming, :analyzed] ->
          update_file(list, :start_loading)
      end

    # list = update_file(list, :start_loading)

    results =
      unfinished_loads
      # altered
      |> Enum.map(&LoadRecordProcessor.perform(&1, setup, notify_fn))

    list = update_file(list, :stop_loading)

    errors =
      results
      |> Enum.filter(&match?({:error, _}, &1))
      |> Enum.map(&elem(&1, 1))

    if length(errors) > 0 do
      notify_fn.("Errors detected in loads: #{inspect(errors)}")
    end
  end

  defp process_lead_file_tasks(
         %List{} = list,
         %Setup{} = setup,
         %LeadFile{} = lead_file,
         notify_fn
       )
       when lead_file == :loaded do
    notify_fn.("FILE ALREADY LOADED.")

    list
  end
end
