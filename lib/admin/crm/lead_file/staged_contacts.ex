defmodule Admin.Crm.LeadFile.StagedContacts do
  @moduledoc """
  Module for working with staged contacts in a lead file.

  This module provides functions for selecting contacts from a lead file, filtering contacts by segment,
  and cleaning up phone numbers based on the segment type.

  It is used not only by the lead file module, but also the SMS batch module to ensure that only valid
  phone numbers are sent to the dialer or other systems.

  See Admin.Crm.LeadFile for more information.
  See Admin.Messaging.OutboundSMSBatch for more information.

  Example usage:

  ```elixir
  # Select all landline contacts from a lead file
  lead_file = Ash.read_first!(Admin.Crm.LeadFile)
  # Each returns a list on plain maps, and unqualified phone numbers have been removed
  landline_contacts = Admin.Crm.LeadFile.StagedContacts.select_segment(lead_file, :landline)
  wireless_contacts = Admin.Crm.LeadFile.StagedContacts.select_segment(lead_file, :wireless)
  ```
  """
  import Ecto.Query, warn: false

  alias Admin.Crm.LeadFile
  alias Admin.Crm.LeadFile.StagedContact

  def phonefields_staged(lf_id) do
    fields = []

    uuid =
      if is_binary(lf_id) do
        {_, uuid} = Ecto.UUID.dump(lf_id)
        uuid
      else
        false
      end

    # {_, uuid} = Ecto.UUID.dump(lf_id)

    result =
      from(
        sc in StagedContact,
        where: sc.lead_file_id == ^uuid,
        select: %{
          homephone: count(sc.homephone),
          companyphone: count(sc.companyphone),
          newphone: count(sc.newphone),
          altcompanyphone: count(sc.altcompanyphone)
        }
      )
      |> Admin.AdminRepo.one()

    result
    |> Map.keys()
    |> Enum.map(fn key ->
      if Map.get(result, key) > 0 do
        fields ++ key
      end
    end)
    |> Enum.filter(fn f -> f != nil end)
  end

  @doc """
  Selects a segment of contacts from a lead file

  ## Parameters
    * `lead_file` - %LeadFile{}: The lead file to select contacts from
    * `segment` - atom: The segment type to select (either :landline or :wireless)
  """
  def select_segment(%LeadFile{id: id}, segment) do
    query =
      from(sc in StagedContact, where: sc.lead_file_id == ^id)
      |> select_as_contact()
      |> filter_segment(segment)

    query
    |> Admin.AdminRepo.Replica.all()
    |> Enum.map(&cleanup_phone_numbers(&1, segment))
  end

  @doc """
  Filters a segment of contacts based on the segment type

  ## Parameters
    * `query` - Ecto.Query: The query to filter
    * `segment` - atom: The segment type to filter by (either :landline or :wireless)
  """
  defp filter_segment(query, :landline) do
    from([sc] in subquery(query),
      where: sc.homephone_line_type in ["landline", "voip"]
    )
  end

  defp filter_segment(query, :wireless) do
    from([sc] in subquery(query),
      where: sc.homephone_line_type == "wireless"
    )
  end

  @helper_fields [
    :landline_loaded?,
    :wireless_loaded?,
    :homephone_dnc?,
    :homephone_line_type,
    :companyphone_dnc?,
    :companyphone_line_type,
    :newphone_dnc?,
    :newphone_line_type,
    :altcompanyphone_dnc?,
    :altcompanyphone_line_type
  ]
  @phone_fields [
    :homephone,
    :companyphone,
    :newphone,
    :altcompanyphone
  ]
  @doc """
  Removes phone numbers that are marked as DNC or Uncallable from the selection.

  This is NOT a destructive operation, it's to prevent wireless or DNC numbers from being added
  to the dialer or other systems under improper circumstances.

  ## Parameters
    * `contact` - map: The contact to clean up
    * `segment` - atom: The segment type to clean up (either :landline or :wireless)
  """
  def cleanup_phone_numbers(contact, segment) do
    @phone_fields
    |> Enum.reduce(contact, fn field, contact ->
      contact
      |> Map.update!(field, &cleanup_phone_number(contact, field, &1, segment))
    end)
    |> Map.drop(@helper_fields)
  end

  @doc """
  Considers each phone number field and removes the phone number if it is marked as DNC, Uncallable,
  or if it is a wireless number in the landline segment.
  """
  defp cleanup_phone_number(%{homephone_dnc?: true}, :homephone, _value, _segment), do: ""
  defp cleanup_phone_number(%{companyphone_dnc?: true}, :companyphone, _value, _segment), do: ""
  defp cleanup_phone_number(%{newphone_dnc?: true}, :newphone, _value, _segment), do: ""

  defp cleanup_phone_number(%{altcompanyphone_dnc?: true}, :altcompanyphone, _value, _segment),
    do: ""

  # defp cleanup_phone_number(%{homephone_line_type: type}, :homephone, _value, :landline)
  #      when type in ["wireless", "Uncallable"], # voip now allows for landline  - remove
  #      do: ""

  # defp cleanup_phone_number(%{companyphone_line_type: type}, :companyphone, _value, :landline)
  #      when type in ["wireless", "Uncallable"], # voip now allows for landline  - remove
  #      do: ""

  # defp cleanup_phone_number(%{newphone_line_type: type}, :newphone, _value, :landline)
  #      when type in ["wireless", "Uncallable"], # voip now allows for landline  - remove
  #      do: ""

  # defp cleanup_phone_number(
  #        %{altcompanyphone_line_type: type},
  #        :altcompanyphone,
  #        _value,
  #        :landline
  #      )
  #      when type in ["wireless", "Uncallable"], # voip now allows for landline  - remove
  #      do: ""

  defp cleanup_phone_number(contact, field, value, _segment) when field in @phone_fields do
    # The following should be safe with all of the above conditions
    # Check if the phone number is not of #{field}_line_type == "Uncallable"
    if contact[:"#{field}_line_type"] != "Uncallable" do
      # If the phone number is not of #{field}_line_type == "Uncallable", return the phone number
      value
    else
      # If the phone number is of #{field}_line_type == "Uncallable", return an empty string
      ""
    end
  end

  @doc """
  Selects all fields as a Map from a contact query and adds helper fields for phone number cleanup.
  """
  defp select_as_contact(%Ecto.Query{} = query) do
    from([sc] in subquery(query),
      select: %{
        # Helper fields
        landline_loaded?: sc.landline_loaded?,
        wireless_loaded?: sc.wireless_loaded?,
        homephone_dnc?: sc.homephone_dnc?,
        homephone_line_type: sc.homephone_line_type,
        companyphone_dnc?: sc.companyphone_dnc?,
        companyphone_line_type: sc.companyphone_line_type,
        newphone_dnc?: sc.newphone_dnc?,
        newphone_line_type: sc.newphone_line_type,
        altcompanyphone_dnc?: sc.altcompanyphone_dnc?,
        altcompanyphone_line_type: sc.altcompanyphone_line_type,

        # contact fields
        accountno: sc.accountno,
        fname: sc.fname,
        minitial: sc.minitial,
        lname: sc.lname,
        fullname: sc.fullname,
        title: sc.title,
        homeadd1: sc.homeadd1,
        homeadd2: sc.homeadd2,
        homeapt: sc.homeapt,
        homecity: sc.homecity,
        homestate: sc.homestate,
        homepostcode: sc.homepostcode,
        homecountry: sc.homecountry,
        homephone: sc.homephone,
        sourcecode: sc.sourcecode,
        pobox: sc.pobox,
        fax: sc.fax,
        email: sc.email,
        companyname: sc.companyname,
        companyadd1: sc.companyadd1,
        companyadd2: sc.companyadd2,
        companycity: sc.companycity,
        companystate: sc.companystate,
        companypostcode: sc.companypostcode,
        companyphone: sc.companyphone,
        newphone: sc.newphone,
        receivername: sc.receivername,
        authfname: sc.authfname,
        authlname: sc.authlname,
        authtitle: sc.authtitle,
        authquest: sc.authquest,
        question1: sc.question1,
        question2: sc.question2,
        question3: sc.question3,
        question4: sc.question4,
        question5: sc.question5,
        question6: sc.question6,
        spoketo: sc.spoketo,
        question4a: sc.question4a,
        question4b: sc.question4b,
        newfirst: sc.newfirst,
        newlast: sc.newlast,
        question13a: sc.question13a,
        question13b: sc.question13b,
        emailannounce: sc.emailannounce,
        emailoffers: sc.emailoffers,
        nameref1: sc.nameref1,
        nameref2: sc.nameref2,
        emailref1: sc.emailref1,
        emailref2: sc.emailref2,
        altcompanyphone: sc.altcompanyphone,
        numberofmembers: sc.numberofmembers,
        misc1: sc.misc1,
        misc2: sc.misc2,
        misc3: sc.misc3,
        misc4: sc.misc4,
        misc5: sc.misc5,
        misc6: sc.misc6,
        misc7: sc.misc7,
        misc8: sc.misc8,
        misc9: sc.misc9,
        misc10: sc.misc10,
        misc11: sc.misc11,
        misc12: sc.misc12,
        misc13: sc.misc13,
        misc14: sc.misc14,
        misc15: sc.misc15,
        misc16: sc.misc16,
        new17: sc.new17,
        new18: sc.new18,
        new19: sc.new19,
        new20: sc.new20,
        new21: sc.new21,
        new22: sc.new22,
        new23: sc.new23,
        new24: sc.new24,
        new25: sc.new25,
        new26: sc.new26,
        new27: sc.new27,
        new28: sc.new28,
        new29: sc.new29,
        new30: sc.new30,
        new31: sc.new31,
        new32: sc.new32,
        new33: sc.new33,
        new34: sc.new34,
        new35: sc.new35,
        new36: sc.new36,
        new37: sc.new37,
        new38: sc.new38,
        new39: sc.new39,
        new40: sc.new40,
        new41: sc.new41,
        new42: sc.new42,
        new43: sc.new43,
        new44: sc.new44,
        new45: sc.new45,
        new46: sc.new46,
        new47: sc.new47,
        new48: sc.new48,
        new49: sc.new49,
        new50: sc.new50,
        new51: sc.new51,
        new52: sc.new52,
        new53: sc.new53,
        new54: sc.new54,
        new55: sc.new55,
        new56: sc.new56,
        new57: sc.new57,
        new58: sc.new58,
        new59: sc.new59,
        new60: sc.new60,
        new61: sc.new61,
        new62: sc.new62,
        new63: sc.new63,
        new64: sc.new64,
        new65: sc.new65,
        new66: sc.new66,
        new67: sc.new67,
        new68: sc.new68,
        new69: sc.new69,
        new70: sc.new70,
        new71: sc.new71,
        new72: sc.new72,
        new73: sc.new73,
        new74: sc.new74,
        new75: sc.new75,
        new76: sc.new76,
        new77: sc.new77,
        new78: sc.new78,
        new79: sc.new79,
        new80: sc.new80,
        new81: sc.new81,
        new82: sc.new82,
        new83: sc.new83,
        new84: sc.new84,
        new85: sc.new85,
        new86: sc.new86,
        new87: sc.new87,
        new88: sc.new88,
        new89: sc.new89,
        new90: sc.new90,
        new91: sc.new91,
        new92: sc.new92,
        new93: sc.new93,
        new94: sc.new94,
        new95: sc.new95,
        new96: sc.new96,
        new97: sc.new97,
        new98: sc.new98,
        new99: sc.new99,
        new100: sc.new100,
        new101: sc.new101,
        new102: sc.new102,
        new103: sc.new103,
        new104: sc.new104,
        new105: sc.new105,
        new106: sc.new106,
        new107: sc.new107,
        new108: sc.new108,
        new109: sc.new109,
        new110: sc.new110,
        new111: sc.new111,
        new112: sc.new112,
        new113: sc.new113,
        new114: sc.new114,
        new115: sc.new115,
        new116: sc.new116,
        new117: sc.new117,
        new118: sc.new118,
        new119: sc.new119,
        new120: sc.new120,
        new121: sc.new121,
        new122: sc.new122,
        new123: sc.new123,
        new124: sc.new124,
        new125: sc.new125,
        new126: sc.new126,
        new127: sc.new127,
        new128: sc.new128,
        new129: sc.new129,
        new130: sc.new130,
        new131: sc.new131,
        new132: sc.new132,
        smsoptin: sc.smsoptin,
        emailoptin: sc.emailoptin
      }
    )
  end

  def disable_all_triggers_cs do
    if not FunWithFlags.enabled?(:skip_recovery_triggers) do
      """
      ALTER TABLE contact_staging DISABLE TRIGGER ALL;
      """
      |> Admin.AdminRepo.query!()

      # Allows DB to update
      :timer.sleep(50000)
    end

    :ok
  end

  def renable_all_triggers_cs do
    if not FunWithFlags.enabled?(:skip_recovery_triggers) do
      """
      ALTER TABLE contact_staging ENABLE TRIGGER ALL;
      """
      |> Admin.AdminRepo.query!()

      # Allows DB to update
      :timer.sleep(50000)
    end

    :ok
  end
end
