defmodule Admin.Crm.Setups do
  @moduledoc """
  The Campaigns context.
  """
  require Logger

  import Ecto.Query, warn: false
  alias Admin.AdminRepo, as: Repo

  alias Admin.Crm.LeadFile
  alias Admin.Crm.LeadFile.{LoadRecords, StagedContacts}
  alias Admin.Crm.LeadFiles
  alias Admin.Crm.Setups.Setup

  # MARK: Fetching
  @doc """
  Returns the list of setups.

  ## Examples

      iex> list_setups()
      [%Setup{}, ...]

  """
  def list_setups do
    Repo.all(Setup)
  end

  def list_setups_desc do
    #### Fetch all Setups in descending order by updated_at
    query =
      from s in Setup,
        select: s,
        order_by: [desc: s.updated_at]

    Repo.all(query)
  end

  def list_setups_desc_15 do
    page = Ash.read!(Setup, action: :page)

    page.results
  end

  def list_setups_desc_bystage(stage) do
    #### Fetch all Setups in descending order by updated_at
    # stage_s = Atom.to_string(stage)

    query =
      from s in Setup,
        where: s.state == ^stage,
        select: s,
        order_by: [desc: s.updated_at]

    Repo.all(query)
  end

  def filtered_setups(search) do
    all_setups = list_setups_desc()

    results =
      all_setups
      |> Enum.filter(fn setup ->
        String.contains?(String.downcase(setup.name), String.downcase(search)) or
          String.contains?(String.downcase(to_string(setup.ticket)), String.downcase(search))
      end)

    # Taking more than `length` records simply
    # returns as many as it can
    Enum.take(results, 10)
  end

  @doc """
  Gets a single setup.

  Raises `Ecto.NoResultsError` if the Setup does not exist.

  ## Examples

      iex> get_setup!(123)
      %Setup{}

      iex> get_setup!(456)
      ** (Ecto.NoResultsError)

  """
  def get_setup!(id), do: Repo.get!(Setup, id)

  # MARK: Mapping
  def get_saved_mappings(lead_files) do
    mappinglist = []

    db_mapping =
      case length(lead_files) do
        0 ->
          [%{}]

        _ ->
          mappinglist =
            lead_files
            |> Enum.map(fn lead_file ->
              %Admin.Crm.LeadFile{id: lead_file_id} = lead_file

              # (lead_file_id, label: "Lead files for mapping fetch")

              result_map = get_saved_mapping(lead_file_id)

              mappinglist ++ result_map
            end)

          # Logger.debug("mappinglist: #{inspect(mappinglist)}") #USE THIS! %{a => %{b: 2, c: 3}}

          mappinglist
      end

    # Logger.debug("finallllyyy : #{inspect(db_mapping)}")

    # Sort by id
    db_mapping
    |> Enum.sort_by(fn map -> map["lf_id"] end)
  end

  def get_saved_mapping(lead_file_id) do
    # mapping = Ash.get!(LeadFileMapping, lead_file_id)
    lf_uuid = Admin.Integrations.DNCScrub.string_to_uuid(lead_file_id)

    mapping =
      from(
        m in "lead_file_mapping",
        where: m.lead_file_id == ^lf_uuid,
        select: %{
          heading_incoming: m.heading_incoming,
          heading_mapped_to: m.heading_mapped_to,
          lead_file_id: m.lead_file_id
        }
      )
      |> Admin.AdminRepo.Replica.all()

    result_map =
      Enum.reduce(mapping, %{}, fn %{
                                     lead_file_id: lead_file_id,
                                     heading_incoming: key,
                                     heading_mapped_to: value
                                   },
                                   acc ->
        lead_file_id = Admin.Integrations.DNCScrub.convert_uuid_tostring(lead_file_id)

        # Logger.debug("before save grab: #{inspect([acc, key, value])}")

        Map.update(acc, lead_file_id, %{key => value}, fn existing_map ->
          # Logger.debug("wthin save grab: #{inspect([ key, value])}")

          Map.put(existing_map, key, value)
        end)
      end)

    # Logger.debug("result_map: #{inspect(result_map)}") #%{a => %{b: 2, c: 3}}

    result_map
  end

  def get_staging_headings do
    headings_from_staging =
      Admin.Crm.LeadFile.StagedContact.regular_fields()
      |> Enum.map(fn heading ->
        Atom.to_string(heading)
      end)

    headings_from_staging2 = ["lead_file_id" | headings_from_staging]

    headings_from_staging2
  end

  def get_saved_mappings_lfids(lead_files) do
    l = []

    saved_leadfile_ids =
      Enum.map(get_saved_mappings(lead_files), fn saved_map ->
        if saved_map != %{} do
          [lf_id] =
            Enum.map(
              saved_map,
              fn {lf_id, mapping} ->
                {lf_id, mapping}

                # Logger.debug("xxx: #{inspect(mapping)}")

                lf_id
              end
            )

          l ++ lf_id
        end
      end)

    saved_leadfile_ids
  end

  def fetch_unsaved_mappings(mapping_data_pairs, lead_files) do
    saved_leadfile_ids = get_saved_mappings_lfids(lead_files)

    # Logger.debug("result ids: #{inspect(saved_leadfile_ids)}") #[lfid, lfid2, lfid3]

    result =
      Enum.filter(mapping_data_pairs, fn map ->
        Map.get(map, "lf_id") not in saved_leadfile_ids
      end)

    # Sort by id
    result
    |> Enum.sort_by(fn map -> map["lf_id"] end)
  end

  def check_duplication(mapped_values) do
    Enum.map(
      # grouped_mapped_headings
      Enum.frequencies(mapped_values),
      fn {mapped, count} ->
        if(count > 1, do: {mapped, true}, else: {mapped, false})
      end
    )
    |> Enum.filter(fn {mapped, dup?} -> dup? == true end)
  end

  def fetch_default_mappings(all_mappings_and_sampledata, lead_files) do
    mapping_data_pairs = mapping_data_pairs(all_mappings_and_sampledata)

    result = fetch_unsaved_mappings(mapping_data_pairs, lead_files)
    # Logger.debug("result map: #{inspect(result)}")

    result
  end

  def mapping_data_pairs(all_mappings_and_sampledata) do
    mappings = []

    mapping_data_pairs =
      all_mappings_and_sampledata
      |> Enum.map(fn file_mapping_and_sampledata ->
        {mapping_data_pair, first5000} = file_mapping_and_sampledata

        Logger.debug("[Setups Mapping] result pairs #{inspect(mapping_data_pair)}")
        Logger.debug("[Setups Mapping] result 000 #{inspect(first5000)}")

        mappings ++ mapping_data_pair
      end)

    # Logger.debug("??: #{inspect(mapping_data_pairs)}")

    mapping_data_pairs
  end

  # MARK: Creating
  @doc """
  Creates a setup.

  ## Examples

      iex> create_setup(%{field: value})
      {:ok, %Setup{}}

      iex> create_setup(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_setup(attrs \\ %{}) do
    %Setup{}
    |> Setup.show(attrs)
    |> Repo.insert()
  end

  # MARK: Updating
  @doc """
  Updates a setup.

  ## Examples

      iex> update_setup(setup, %{field: new_value})
      {:ok, %Setup{}}

      iex> update_setup(setup, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_setup(%Setup{} = setup, attrs) do
    setup
    |> Setup.show(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a setup.

  ## Examples

      iex> delete_setup(setup)
      {:ok, %Setup{}}

      iex> delete_setup(setup)
      {:error, %Ecto.Changeset{}}

  """
  def delete_setup(%Setup{} = setup) do
    Repo.delete(setup)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking setup changes.

  ## Examples

      iex> change_setup(setup)
      %Ecto.Changeset{data: %Setup{}}

  """
  def change_setup(%Setup{} = setup, attrs \\ %{}) do
    Setup.changeset(setup, attrs)
  end

  def fetch_setup_leadfile(setup_job_id) do
    lead_files = Repo.all(LeadFile)

    result =
      lead_files
      |> Enum.filter(fn leadfile ->
        leadfile.last_job_id == setup_job_id
      end)
      |> Enum.take(1)

    # %LeadFile{id: id, name: name, url: url, file_size: file_size, file_format: file_format, inserted_at: inserted_at, updated_at: updated_at} = lead_file

    [lead_file | _rest] = result

    %{
      id: lead_file.id,
      name: lead_file.name,
      url: lead_file.url
      # file_size: lead_file.file_size,
      # file_format: lead_file.file_format,
      # inserted_at: lead_file.inserted_at,
      # updated_at: lead_file.updated_at
    }
  end

  @doc
  """
  This function is the entrypoint for the recovery process.
  It will call the appropriate function based on the recovery type and method choice.

  Recovery Types:
  - :setup
  - :leadfile

  Leadfile is only required for :leadfile recovery type.

  Method Choices:
  - :delete_loads
  - :delete_all


  """

  def recovery_entrypoint(%Setup{} = setup, recovery_type, method_choice, leadfile \\ nil) do
    Logger.debug("[Setups] Recovery Entrypoint")

    # Broadcast initial step
    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_general, "Starting recovery process... (Trigger disabling 50s wait)"}
    )

    # Broadcast initial step
    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_general, "Trigger disabling...50s wait"}
    )

    # Disable all triggers on contact_staging, if successful do setuprecovery
    recovery_result =
      case StagedContacts.disable_all_triggers_cs() do
        :ok ->
          Logger.debug("[Setups - Staged Contacts] Disabled all triggers")

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_general, "Disabled all contact_staging triggers"}
          )

          case recovery_type do
            "setup" ->
              setup_cleanup(setup, method_choice)

            "leadfile" ->
              leadfile_cleanup(leadfile, method_choice, setup)
          end

          # Final step: Complete recovery
          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_general, "Recovery completed successfully!"}
          )

          :ok

        {:error, error} ->
          Logger.warning("[Setups - Staged Contacts]Error disabling triggers: #{error}")
          :error
      end

    # Re-enable triggers and broadcast completion
    result =
      case recovery_result do
        :ok ->
          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_general, "Re-enabling database triggers...50s wait"}
          )

          if StagedContacts.renable_all_triggers_cs() == :ok do
            Phoenix.PubSub.broadcast(
              Admin.PubSub,
              AdminWeb.SetupLive.Process.recovery_topic(setup),
              {:recovery_complete, :ok}
            )

            :ok
          else
            Phoenix.PubSub.broadcast(
              Admin.PubSub,
              AdminWeb.SetupLive.Process.recovery_topic(setup),
              {:recovery_complete, :error}
            )

            :error
          end

        :error ->
          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_complete, :error}
          )

          :error
      end

    result
  end

  @doc
  """
  This function recovers a setup and all associated lead_files.
  Overall the setup and all associated lead_files will be reset to :new
  """

  def setup_cleanup(%Setup{} = setup, method_choice) when is_atom(method_choice) do
    # Step 1: Setup recovery
    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_step, "Recovering setup..."}
    )

    # Update Setup.state to :new
    setup
    |> Ash.Changeset.for_update(:recover, %{})
    |> Ash.update!()

    # Step 2: Process each lead file based on choice
    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_general, "Processing lead files..."}
    )

    # Process each lead file
    setup.lead_files
    |> Enum.map(fn lf ->
      leadfile_cleanup(lf, method_choice, setup)
    end)
  end

  # For backward compatibility
  def setup_cleanup(setup_id, choice) when is_binary(setup_id) and is_atom(choice) do
    setup = Ash.get!(Setup, setup_id, load: [lead_files: [:operations, :loads]])
    setup_cleanup(setup, choice)
  end

  """
  This function and related functions allows for leadfile recovery through two methods.

  1. Lead Loaded Cleanup
    - Remove all Contact & Dial leads
    - Remove all leads in ViciLists

  2. Complete Cleanup
    - Remove all Contact & Dial leads
    - Remove all leads in ViciLists
    - Delete Child Projects
    - On both Land + Wireless
      - Delete ViciList List w/ Leads (vicidial_list)
      - Delete Campaign (vicidial_campaigns)
      - Remove CRM_Mapping entry (Campaign -> Parent)
    - Move Parents to Trash Project 2 - CampaignID CM2
    - Reset LoadRecords (lead_file_loads)
    - Cleanup CreatedProjects (lead_file_created_projects)

  Each leadfile will be reset to :new

  """

  def leadfile_cleanup(lf, method_choice, setup) do
    # Step 3: Lead file reset
    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_step, "Resetting lead file #{lf.name}..."}
    )

    # Update LeadFile.state to :new
    lf
    |> Ash.Changeset.for_update(:reset, %{})
    |> Ash.update!()

    lf.loads
    |> Enum.map(fn load ->
      l_projs = [load.created_l, load.created_l_group, load.created_l_container]
      w_projs = [load.created_w, load.created_w_group, load.created_w_container]
      parents = [load.parent_id_landline, load.parent_id_wireless]

      case method_choice do
        :delete_loads ->
          # Process landline if loaded
          if load.load_landline? do
            trash_leads(
              :landline,
              load.dialer_landline,
              load.dialer_wireless,
              l_projs,
              lf.id,
              setup
            )
          end

          # Process wireless if loaded
          if load.load_wireless? do
            trash_leads(
              :wireless,
              load.dialer_landline,
              load.dialer_wireless,
              w_projs,
              lf.id,
              setup
            )
          end

        :delete_all ->
          # Process landline if loaded
          if load.load_landline? do
            trash_all(
              :landline,
              load.dialer_landline,
              load.dialer_wireless,
              l_projs,
              parents,
              load.id,
              lf.id,
              setup
            )
          end

          # Process wireless if loaded
          if load.load_wireless? do
            trash_all(
              :wireless,
              load.dialer_landline,
              load.dialer_wireless,
              w_projs,
              parents,
              load.id,
              lf.id,
              setup
            )
          end
      end
    end)
  end

  def trash_leads(segment, landline_loaded_on, wireless_loaded_on, projs, lfid, setup) do
    # Remove staged contacts for lead file
    result =
      case Admin.Crm.List.destage_contacts2(lfid) do
        :ok ->
          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step, "StagedContacts removed"}
          )

          :ok

        {:error, error} ->
          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step, "Error removing StagedContacts: #{error}"}
          )

          Logger.warning("Error removing contacts from staging: #{error}")
      end

    projs
    |> Enum.map(fn proj ->
      case is_nil(proj) do
        true ->
          proj

        false ->
          # Remove Contact & Dial leads
          # contact leads.
          Crm.Contacts._cleanup_import(proj)

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step, "Contact Leads for #{proj} removed"}
          )

          Logger.debug("Contact Leads for #{proj} removed")

          # dial leads
          Crm.Contacts._cleanup_dial_import(proj)

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step, "Dial Leads for #{proj} removed"}
          )

          Logger.debug("Dial Leads for #{proj} removed")

          # Remove Leads in ViciList
          loaded_on = if segment == :landline, do: landline_loaded_on, else: wireless_loaded_on

          Logger.debug("Loaded on sent #{String.to_atom(loaded_on)}")
          Dialer.public_import_cleanup(proj, String.to_atom(loaded_on))
          Logger.debug("Vici Leads for #{proj} removed")

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step, "Removed Vicidial #{loaded_on} Leads for #{proj}"}
          )
      end
    end)
  end

  def trash_created(
        segment,
        landline_loaded_on,
        wireless_loaded_on,
        projs,
        parents,
        load_id,
        lfid,
        setup
      ) do
    projs
    |> Enum.map(fn proj ->
      case is_nil(proj) do
        true ->
          proj

        false ->
          # Delete Child Projects
          Crm.Projects._project_cleanup(proj)
          Logger.debug("Child project #{proj} removed")

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step, "Child Project #{proj} removed"}
          )

          # L+W
          # Delete ViciList List w/ Leads (vicidial_list)
          # Delete Campaign (vicidial_campaigns)
          # Remove CRM_Mapping entry (Campaign -> Parent)
          loaded_on = if segment == :landline, do: landline_loaded_on, else: wireless_loaded_on
          Dialer.ViciDial._vicidial_created_cleanup(proj, String.to_atom(loaded_on))
          Logger.debug("ViciDial list, campaign and CRMMapping #{proj} removed")

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            AdminWeb.SetupLive.Process.recovery_topic(setup),
            {:recovery_step,
             "ViciDial #{loaded_on} List, Campaign and CRMMapping for #{proj} removed"}
          )
      end
    end)

    # Move Parents to Trash Project 2 - CampaignID CM2
    Crm.Projects._parent_update(parents)
    Logger.debug("Parents #{inspect(parents)} campaign updated to CM2 (ProjectID 2)")

    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_step, "Parents #{inspect(parents)} campaign updated to CM2 (ProjectID 2)"}
    )

    # Reset LoadRecords
    LoadRecords.reset_loaded(load_id)
    Logger.debug("LoadRecords reset")

    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_step, "LoadRecords Reset"}
    )

    # Cleanup CreatedProjects
    LeadFiles.created_project_cleanup(lfid)
    Logger.debug("CreatedProject Cleanup")

    Phoenix.PubSub.broadcast(
      Admin.PubSub,
      AdminWeb.SetupLive.Process.recovery_topic(setup),
      {:recovery_step, "CreatedProjects Cleanup"}
    )
  end

  def trash_all(
        segment,
        landline_loaded_on,
        wireless_loaded_on,
        projs,
        parents,
        load_id,
        lfid,
        setup \\ nil
      ) do
    trash_leads(segment, landline_loaded_on, wireless_loaded_on, projs, lfid, setup)

    trash_created(
      segment,
      landline_loaded_on,
      wireless_loaded_on,
      projs,
      parents,
      load_id,
      lfid,
      setup
    )
  end
end
