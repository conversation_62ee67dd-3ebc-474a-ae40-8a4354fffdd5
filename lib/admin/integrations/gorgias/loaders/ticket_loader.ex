defmodule Admin.Integrations.Gorgias.Loaders.TicketLoader do
  @moduledoc """
  This module is responsible for loading tickets from Gorgias.

  This is the most involved loader within the Gorgias integration.

  In addition to loading the ticket records themselves, this module also loads the custom fields and tags associated with each ticket.
  """
  use Admin.Integrations.Gorgias.Loaders.GorgiasLoader,
    cursor_id: 1,
    cursor_name: "ticket"

  require Logger

  alias Admin.AdminRepo
  alias Admin.Integrations.Gorgias.{CustomField, Loader, Tag, Ticket, TicketCustomField}
  alias Ash.Changeset
  import Ecto.Query, warn: false

  @shopify_integration_id :"34482"

  @impl Loader
  def retrieve(tenant, cursor, opts) do
    Logger.debug("Retrieving tickets for #{inspect(tenant.id)}")

    tenant
    |> RestAPI.tickets(cursor, opts)
    |> parse_api_result()
  end

  @impl Loader
  def process_item(tenant, ticket, _opts) do
    Logger.debug("Processing ticket #{inspect(ticket.id)} for #{inspect(tenant.id)}")

    parsed_ticket =
      ticket
      |> parse_api_ticket_blob(tenant)

    Ticket
    |> Changeset.for_create(:import, parsed_ticket, tenant: tenant)
    |> Ash.create!(tenant: tenant, upsert?: true)

    ticket
    |> parse_api_custom_field_blob!(tenant)

    :ok
  rescue
    error ->
      Logger.error(
        "Error processing ticket: #{inspect(error)}\n#{Exception.format_stacktrace(error)}"
      )

      {:error, Exception.message(error)}
  end

  @doc """
  Takes a ticket from the API and extracts the bits that Ticket needs, upserting tags as needed.
  """
  def parse_api_ticket_blob(ticket, tenant) do
    tags =
      ticket.tags
      |> Enum.map(&parse_upsert_tag/1)
      |> Enum.map(fn tag ->
        if !tag.name || tag.name == "" || String.trim(tag.name) == "" do
          raise "Tag has no name: #{inspect(tag)}"
        end

        Tag.upsert!(tag.name, tag.tag_color, tenant: tenant)
      end)
      |> Enum.map(& &1.id)

    # This may not be present on bulk lookups.
    customer_id =
      ticket
      |> get_in([:customer, :integrations, @shopify_integration_id, :customer, :id])

    # This is present on all tickets
    gorgias_customer_id = ticket.customer.id

    ticket
    |> Map.put(:gorgias_customer_id, gorgias_customer_id)
    |> Map.put(:shopify_customer_id, customer_id)
    |> Map.put(:tags, tags)
    |> Map.put(:assignee_team, _parse_assignee_team(ticket.assignee_team))
  end

  defp parse_upsert_tag(tag),
    do: %{
      id: tag.id,
      name: tag.name || "No Tag Name",
      tag_color: tag |> _parse_color()
    }

  def parse_custom_field_result({:ok, %{status: 200, body: body}}, tenant) do
    custom_field_input = body

    parsed_custom_field =
      %{
        id: custom_field_input.id,
        object_type: custom_field_input.object_type,
        label: custom_field_input.label,
        description: custom_field_input.description,
        priority: custom_field_input.priority,
        required: custom_field_input.required
      }

    custom_field =
      CustomField
      |> Changeset.for_create(:upsert, parsed_custom_field, tenant: tenant)
      |> Ash.create!(tenant: tenant)

    {:ok, custom_field}
  end

  @doc """
  This function will load a custom field from the Gorgias API and upsert it into the database.
  """
  def load_custom_field(tenant, id) do
    {:ok, _custom_fields} =
      tenant
      |> RestAPI.custom_field(id)
      |> parse_custom_field_result(tenant)

    :ok
  end

  @doc """
  This function will take a ticket from the API and extract the custom fields from it.
  It will then upsert the custom fields into the database.
  """
  def parse_api_custom_field_blob!(ticket, tenant) do
    # Extract custom fields from the ticket and upsert them into the database
    ticket.custom_fields
    |> Enum.map(fn {_field, values} ->
      %{
        ticket_id: ticket.id,
        custom_field_id: values.id,
        value: values.value
      }
    end)
    |> Enum.each(fn field ->
      load_custom_field(tenant, field.custom_field_id)

      TicketCustomField
      |> Changeset.for_create(:upsert, field, tenant: tenant)
      |> Ash.create!(tenant: tenant)
    end)

    :ok
  end

  # MARK: Ticket Helpers

  @doc """
  This function will grab a batch of tickets from the database and look up
  the Shopify & Gorgias Customer IDs for each ticket.

  The ticket will be updated to contain the customer IDs, and the `shopify_customer_id_looked_up`
  field will be set to true.

  This is a self reflecting operation, as in, it will attempt to look up customer information from
  our database before we ask the API. This is because of the 40 request per second limit.

  Options:
  - `:recursive` - If true, this function will call itself after finishing the batch.
  - `:limit` - The maximum number of tickets to look up. Default is 100.
  """
  @type lookup_opts :: {:recursive, boolean()} | {:limit, integer()}
  @spec lookup_customers(Tenant.t()) :: :ok
  @spec lookup_customers(Tenant.t(), [lookup_opts]) :: :ok
  def lookup_customers(tenant, opts \\ []) do
    recursive = Keyword.get(opts, :recursive, false)
    limit = Keyword.get(opts, :limit, 100)
    # This function should look up the Shopify order IDs for each ticket
    # and update the tickets with the order IDs.
    #
    # The Shopify order ID should be stored in the `shopify_order_id` field
    # and the Shopify order name should be stored in the `shopify_order_name` field.
    batch =
      from(t in Ticket,
        where: t.shopify_customer_id_looked_up == false and t.tenant_id == ^tenant.id,
        limit: ^limit,
        order_by: [desc: t.created_datetime],
        select: t
      )
      |> AdminRepo.Replica.all(prefix: "metrics")

    case batch do
      [] ->
        :ok

      _ ->
        for ticket <- batch do
          updated_ticket_args =
            tenant
            |> lookup_integration_information(ticket)
            |> Map.from_struct()
            |> Enum.map(fn {k, v} -> {Atom.to_string(k), v} end)

          ticket
          |> Changeset.for_update(:update, updated_ticket_args, tenant: tenant)
          |> Ash.update!(tenant: tenant)
        end
    end

    case recursive do
      true -> lookup_customers(tenant, opts)
      false -> :ok
    end
  end

  def lookup_integration_information(tenant, %Ticket{gorgias_customer_id: nil} = ticket) do
    case RestAPI.ticket(tenant, ticket.id) do
      {:ok, %{status: 200, body: live_ticket}} ->
        # We have a ticket, we now know the customer ID
        # Update information in this ticket by itself
        gcid = live_ticket |> get_in([:customer, :id])

        scid =
          live_ticket
          |> get_in([:customer, :integrations, @shopify_integration_id, :customer, :id])

        ticket
        |> Map.merge(%{
          gorgias_customer_id: gcid,
          shopify_customer_id: scid,
          shopify_customer_id_looked_up: true
        })

      {:ok, %{status: 404}} ->
        # Ticket not found, we will remove this from the batch by simply marking
        # the ticket as looked up.
        ticket
        |> Map.put(:shopify_customer_id_looked_up, true)
    end
  end

  def lookup_integration_information(
        tenant,
        %Ticket{gorgias_customer_id: gcid, shopify_customer_id: nil} = ticket
      )
      when not is_nil(gcid) do
    # We have a gorgias customer, so we likely have other tickets with fetched Shopify customer IDs
    # We will try to patch this without an API call from our own DB.
    query =
      from(t in Ticket,
        where:
          t.gorgias_customer_id == ^gcid and
            not is_nil(t.shopify_customer_id) and
            t.shopify_customer_id_looked_up == true and
            t.tenant_id == ^tenant.id,
        limit: 1,
        select: t
      )

    case query |> AdminRepo.one(prefix: "metrics") do
      nil ->
        # No existing tickets with the shopify ID
        # We will cheat here by removing the customer ID and fetching the ticket with existing logic
        ticket =
          ticket
          |> Map.put(:gorgias_customer_id, nil)

        tenant
        |> lookup_integration_information(ticket)

      existing_ticket ->
        ticket
        |> Map.put(:shopify_customer_id, existing_ticket.shopify_customer_id)
        |> Map.put(:shopify_customer_id_looked_up, true)
    end
  end

  # MARK: Internal Helpers
  defp parse_upsert_tag(%{name: ""} = tag),
    do: %{
      id: tag.id,
      name: "No Tag Name",
      tag_color: tag |> _parse_color()
    }

  defp _parse_color(%{decoration: %{color: color}}), do: color
  defp _parse_color(_), do: "#453eab"

  defp _parse_assignee_team(%{name: name}), do: name
  defp _parse_assignee_team(_), do: "No Team"
end
