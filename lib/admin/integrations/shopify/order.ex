defmodule Admin.Integrations.Shopify.Order do
  @moduledoc """
  This module represents an order within Shopify.
  """
  use Ash.Resource,
    domain: Admin.Integrations.Shopify.Domain,
    data_layer: AshPostgres.DataLayer

  alias Admin.Integrations.Shopify.{Domain, LineItem, OrderTag, Refund, Tag}

  code_interface do
    define :from_api, args: [:shopify_id, :order_id, :source, :created_at, :meta_tag]
    define :import, args: [:shopify_id, :order_id, :source, :created_at, :meta_tag]
  end

  postgres do
    table "shopify_orders"
    schema "metrics"
    repo Admin.AdminRepo
  end

  identities do
    identity :order_id, [:order_id]
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      public? true
      description "The Order 'Name' from spotify; ABC1234"
    end

    attribute :shopify_id, :string do
      public? true
      description "The Order 'ID' from spotify; gid://shopify/Order/1234567890"
    end

    attribute :email, :string do
      public? true
    end

    # fulfilled, unfulfilled, partial, refunded, partially-refunded
    attribute :status, :string do
      public? true
    end

    attribute :created_at, :utc_datetime do
      public? true
    end

    attribute :source, :string do
      public? true
    end

    attribute :customer_id, :string do
      public? true
    end

    attribute :meta_tag, :string do
      public? true
    end

    attribute :discount_codes, :string do
      public? true
    end

    # Pricing
    ## Current
    attribute :current_subtotal_price_set, :float do
      public? true

      description "The sum of the prices for all line items after discounts and returns, in shop and presentment currencies. If `taxesIncluded` is `true`, then the subtotal also includes tax."

      allow_nil? true
    end

    attribute :current_total_discounts_set, :float do
      public? true

      description "The total amount discounted on the order after returns, in shop and presentment currencies. This includes both order and line level discounts."

      allow_nil? true
    end

    attribute :current_total_duties_set, :float do
      public? true

      description "The total amount of duties after returns, in shop and presentment currencies. Returns `null` if duties aren't applicable."

      allow_nil? true
    end

    attribute :current_total_price_set, :float do
      public? true

      description "The total price of the order, after returns, in shop and presentment currencies. This includes taxes and discounts."

      allow_nil? true
    end

    attribute :current_total_tax_set, :float do
      public? true

      description "The sum of the prices of all tax lines applied to line items on the order, after returns, in shop and presentment currencies."

      allow_nil? true
    end

    ## Statements
    attribute :net_payment_set, :float do
      public? true

      description "The net payment for the order, based on the total amount received minus the total amount refunded, in shop and presentment currencies."

      allow_nil? true
    end

    attribute :refund_discrepancy_set, :float do
      public? true

      description "The difference between the suggested and actual refund amount of all refunds that have been applied to the order. A positive value indicates a difference in the merchant's favor, and a negative value indicates a difference in the customer's favor."

      allow_nil? true
    end

    ## Original
    attribute :original_total_duties_set, :float do
      public? true

      description "The total amount of duties before returns, in shop and presentment currencies. Returns `null` if duties aren't applicable."

      allow_nil? true
    end

    attribute :original_total_price_set, :float do
      public? true

      description "The total price of the order at the time of order creation, in shop and presentment currencies."

      allow_nil? true
    end

    attribute :subtotal_price_set, :float do
      public? true

      description "The sum of the prices for all line items after discounts and before returns, in shop and presentment currencies. If `taxesIncluded` is `true`, then the subtotal also includes tax."

      allow_nil? true
    end

    ## Totals
    attribute :total_capturable_set, :float do
      public? true

      description "The authorized amount that is uncaptured or undercaptured, in shop and presentment currencies. This amount isn't adjusted for returns."

      allow_nil? true
    end

    attribute :total_discounts_set, :float do
      public? true

      description "The total amount discounted on the order before returns, in shop and presentment currencies. This includes both order and line level discounts."

      allow_nil? true
    end

    attribute :total_outstanding_set, :float do
      public? true

      description "The total amount not yet transacted for the order, in shop and presentment currencies. A positive value indicates a difference in the merchant's favor (payment from customer to merchant) and a negative value indicates a difference in the customer's favor (refund from merchant to customer)."

      allow_nil? true
    end

    attribute :total_price_set, :float do
      public? true

      description "The total price of the order, before returns, in shop and presentment currencies. This includes taxes and discounts."

      allow_nil? true
    end

    attribute :total_received_set, :float do
      public? true

      description "The total amount received from the customer before returns, in shop and presentment currencies."

      allow_nil? true
    end

    attribute :total_refunded_set, :float do
      public? true
      description "The total amount that was refunded, in shop and presentment currencies."
      allow_nil? true
    end

    attribute :total_refunded_shipping_set, :float do
      public? true

      description "The total amount of shipping that was refunded, in shop and presentment currencies."

      allow_nil? true
    end

    attribute :total_shipping_price_set, :float do
      public? true

      description "The total shipping amount before discounts and returns, in shop and presentment currencies."

      allow_nil? true
    end

    attribute :total_tax_set, :float do
      public? true
      description "The total tax amount before returns, in shop and presentment currencies."
      allow_nil? true
    end

    attribute :total_tip_received_set, :float do
      public? true
      description "The sum of all tip amounts for the order, in shop and presentment currencies."
      allow_nil? true
    end

    # End Pricing
  end

  relationships do
    has_many :line_items, LineItem do
      public? true
    end

    has_many :refunds, Refund do
      public? true
      source_attribute :order_id
      destination_attribute :order_id
    end

    many_to_many :tags, Tag do
      public? true
      through OrderTag
      # on this resource
      source_attribute :id
      # matching join source
      source_attribute_on_join_resource :order_id

      # destnation resource (Tag)
      destination_attribute :id
      # matching join destination
      destination_attribute_on_join_resource :tag_id
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :from_api do
      skip_unknown_inputs :*
      accept [:shopify_id, :order_id, :source, :created_at, :meta_tag]
    end

    update :update_tags do
      accept :*
      argument :tags, {:array, :uuid}

      change manage_relationship(:tags, type: :append_and_remove)
    end

    update :update_refunds do
      accept :*
      argument :refunds, {:array, :integer}

      change manage_relationship(:refunds, type: :append_and_remove)
    end

    create :import do
      skip_unknown_inputs :*
      accept :*
      upsert? true
      upsert_identity :order_id
    end
  end
end
