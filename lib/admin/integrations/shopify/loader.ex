defmodule Admin.Integrations.Shopify.Loader do
  @moduledoc """
  This module is a loader for Shopify integrations.

  Unlike the Gorgias integration, it is not at this time tenantized.
  """
  alias Admin.Integrations.Shopify.{
    Domain,
    LineItem,
    Order,
    Refund,
    RefundLineItem,
    State
  }

  alias Admin.Integrations.Shopify.Intermediary
  alias Admin.Integrations.Shopify.Intermediary.Page
  alias Ash.Changeset

  @orders_per_page 20
  defp orders_per_page, do: @orders_per_page
  @refund_orders_per_page 150
  defp refund_orders_per_page, do: @refund_orders_per_page
  @line_items_per_page 8
  defp line_items_per_page, do: @line_items_per_page
  @line_items_per_fetch 250
  defp line_items_per_fetch, do: @line_items_per_fetch
  @cursor_id 1
  def cursor_id, do: @cursor_id
  @refund_cursor_id 2
  def refund_cursor_id, do: @refund_cursor_id
  @fetch_retry_limit 50

  @filter "processed_at:>=2023-01-01"
  defp filter, do: @filter

  @refund_filter "processed_at:>=2023-06-01 financial_status:\\\"partially_refunded,refunded\\\""
  defp refund_filter, do: @refund_filter

  def setup do
    access_key =
      Application.get_env(:admin, :shopify_access_token, "shbob_123424242333424242fdeadbeef")

    url =
      Application.get_env(
        :admin,
        :shopify_url,
        "https://some-merchant.myshopify.com/admin/api/graphql.json"
      )

    Neuron.Config.set(url: url)
    Neuron.Config.set(headers: ["X-Shopify-Access-Token": access_key])
    :ok
  end

  @type cursor() :: String.t() | nil
  @spec start(cursor()) :: {:ok, cursor()} | {:error, any()}
  def start(cursor \\ nil) do
    setup()

    stream(filter(), cursor)
  end

  @spec start_refunds(cursor()) :: {:ok, cursor()} | {:error, any()}
  def start_refunds(cursor \\ nil) do
    setup()

    stream(refund_filter(), cursor,
      cursor_id: refund_cursor_id(),
      query_fn: &paged_refunds_request_body/3
    )
  end

  @spec stream(String.t(), cursor()) :: {:ok, cursor()} | {:error, any()}
  def stream(filter, cursor, opts \\ []) do
    reverse = Keyword.get(opts, :reverse, false)
    cursor_id = Keyword.get(opts, :cursor_id, cursor_id())
    query_fn = Keyword.get(opts, :query_fn, &paged_request_body/3)

    query_string = query_fn.(filter, cursor, reverse)

    %Page{} = page = fetch_query_to_page(query_string)

    page.items
    |> Enum.map(&Intermediary.Order.from_api/1)
    |> Enum.each(&insert_order/1)

    if page.has_next_page do
      page.next_cursor |> maybe_update_cursor(cursor_id)

      stream(filter, page.next_cursor, opts)
    else
      {:ok, page.next_cursor}
    end
  end

  @spec fetch_all_line_items_for_order_id(String.t()) :: [Intermediary.LineItem.t()]
  def fetch_all_line_items_for_order_id("gid://shopify/Order/" <> order_id) do
    query_string = line_items_request_body(order_id, nil)

    %Page{} = page = fetch_query_to_page(query_string)

    case page.items |> hd() do
      %{} = order ->
        order
        |> Intermediary.Order.from_api()
        |> then(& &1.line_items)

      _ ->
        []
    end
  end

  defp parse_id("gid://shopify/Order/" <> _order_id = id), do: id
  defp parse_id("gid://shopify/Refund/" <> refund_id), do: refund_id |> String.to_integer()

  defp parse_id("gid://shopify/LineItem/" <> line_item_id),
    do: line_item_id |> String.to_integer()

  defp parse_id(id) when is_binary(id), do: id |> String.to_integer()
  defp parse_id(id) when is_integer(id), do: id
  defp parse_id(something_else), do: something_else

  def insert_order(%Intermediary.Order{} = order) do
    order_args = order |> Intermediary.Order.to_args()

    order_item =
      Order
      |> Ash.Changeset.for_create(:import, order_args)
      |> Ash.create!()

    order.refunds
    |> Enum.each(fn refund -> insert_refund(refund) end)

    {:ok, order_item} =
      order
      |> update_pricing_fields(order_item)

    order.line_items
    |> Enum.each(fn %Intermediary.LineItem{} = li ->
      LineItem.import(
        li.sku,
        li.name,
        order_item.id,
        order.name,
        li.quantity,
        li.current_quantity,
        li.status,
        li.price_discounted,
        li.price_original
      )
    end)

    order
  end

  @spec load_refund_for_insert(refund_id :: non_neg_integer()) ::
          Intermediary.Refund.t() | {:error, any()}
  def load_refund_for_insert(refund_id) do
    setup()

    query_string = paged_refund_body(refund_id, nil)

    {:ok, %{"refund" => paged_refund}} = fetch_query(query_string)

    paged_refund
    |> select_all_refund_line_items()
    |> to_args()
  end

  defp select_all_refund_line_items(
         %{"refundLineItems" => %{"pageInfo" => %{"hasNextPage" => false}}} = paged_refund
       ) do
    paged_refund
  end

  defp select_all_refund_line_items(
         %{"refundLineItems" => %{"pageInfo" => %{"hasNextPage" => true}}} = paged_refund
       ) do
    refund_id = paged_refund["id"]
    items = paged_refund["refundLineItems"]["nodes"]
    next_cursor = paged_refund["refundLineItems"]["pageInfo"]["endCursor"]

    query_string = paged_refund_body(refund_id, next_cursor)

    {:ok, %{"refund" => paged_refund}} = fetch_query(query_string)

    items = items ++ paged_refund["refundLineItems"]["nodes"]

    %{paged_refund | "refundLineItems" => %{"nodes" => items}}
    |> select_all_refund_line_items()
  end

  # Mainly drops nodes and page info out.
  defp to_args(paged_refund) do
    refund_line_items = paged_refund["refundLineItems"]["nodes"]

    paged_refund
    |> Map.delete("refundLineItems")
    |> Map.put("refund_line_items", refund_line_items)
  end

  def insert_refund(refund) do
    refund_item =
      Refund
      |> Ash.Changeset.for_create(:import, _parse_args(refund))
      |> Ash.create!()

    refund
    |> Map.get("refund_line_items", [])
    |> Enum.map(fn %{} = rli ->
      RefundLineItem
      |> Ash.Changeset.for_create(:import, %{
        # Composite Key
        refund_id: refund_item.id,
        line_item_sku: rli |> Map.get("lineItem") |> Map.get("sku"),

        # Other Fields
        line_item_name: rli |> Map.get("lineItem") |> Map.get("name"),
        quantity: rli |> Map.get("quantity"),
        restock_type: rli |> Map.get("restockType"),
        restocked?: rli |> Map.get("restocked"),
        price_set: rli |> Map.get("priceSet") |> Map.get("presentmentMoney") |> Map.get("amount")
      })
      |> Ash.create!()
    end)
  end

  defp _parse_args(%{"createdAt" => created_at} = refund) when not is_nil(created_at) do
    %{
      id: refund |> Map.get("id") |> parse_id(),
      shopify_id: refund |> Map.get("order") |> Map.get("id") |> parse_id(),
      order_id: refund |> Map.get("order") |> Map.get("name"),
      created_at: refund |> Map.get("createdAt"),
      updated_at: refund |> Map.get("updatedAt"),
      note: refund |> Map.get("note"),
      total_refunded_set:
        refund |> Map.get("totalRefundedSet") |> Map.get("shopMoney") |> Map.get("amount")
    }
  end

  defp _parse_args(%{id: _id} = refund) do
    %{
      id: refund |> Map.get(:id) |> parse_id()
    }
  end

  # If the order has no pricing fields, don't update them. It's likely a partial/refund order.
  defp update_pricing_fields(
         %Intermediary.Order{current_subtotal_price_set: nil},
         %Order{} = order_item
       ),
       do: {:ok, order_item}

  defp update_pricing_fields(%Intermediary.Order{} = order, %Order{} = order_item) do
    order_item
    |> Ash.Changeset.for_update(:update,
      current_subtotal_price_set: order.current_subtotal_price_set,
      current_total_discounts_set: order.current_total_discounts_set,
      current_total_price_set: order.current_total_price_set,
      current_total_tax_set: order.current_total_tax_set,
      net_payment_set: order.net_payment_set,
      refund_discrepancy_set: order.refund_discrepancy_set,
      original_total_price_set: order.original_total_price_set,
      subtotal_price_set: order.subtotal_price_set,
      total_capturable_set: order.total_capturable_set,
      total_discounts_set: order.total_discounts_set,
      total_price_set: order.total_price_set,
      total_received_set: order.total_received_set,
      total_refunded_set: order.total_refunded_set,
      total_refunded_shipping_set: order.total_refunded_shipping_set,
      total_shipping_price_set: order.total_shipping_price_set,
      total_tax_set: order.total_tax_set
    )
    |> Ash.update()
  end

  defp maybe_update_cursor(nil, _cursor_id), do: nil

  defp maybe_update_cursor(cursor, cursor_id) when is_binary(cursor) do
    Ash.get!(State, cursor_id)
    |> Changeset.for_update(:update, %{cursor: cursor})
    |> Ash.update!()
  end

  defp parse_cursor(cursor) when is_nil(cursor), do: "null"
  defp parse_cursor(cursor), do: "\"#{cursor}\""

  defp page_options(cursor, false),
    do: "first: #{orders_per_page()}, after: #{cursor |> parse_cursor()}"

  defp page_options(cursor, true),
    do: "last: #{orders_per_page()}, before: #{cursor |> parse_cursor()}"

  defp refund_page_options(cursor, false),
    do: "first: #{refund_orders_per_page()}, after: #{cursor |> parse_cursor()}"

  defp refund_page_options(cursor, true),
    do: "last: #{refund_orders_per_page()}, before: #{cursor |> parse_cursor()}"

  defp paged_request_body(query, cursor, reverse?),
    do: """
    query {
      orders(query: "#{query}", #{page_options(cursor, reverse?)}) {
        nodes {
          id
          name
          createdAt
    		  customer {id}
          meta_tag: metafield(namespace: "custom", key: "order_tag") {value}
          discountCodes

          currentCartDiscountAmountSet  {shopMoney{amount currencyCode}}
          currentSubtotalPriceSet  {shopMoney{amount currencyCode}}
          currentTotalDiscountsSet  {shopMoney{amount currencyCode}}
          currentTotalPriceSet  {shopMoney{amount currencyCode}}
          currentTotalTaxSet  {shopMoney{amount currencyCode}}

          netPaymentSet  {shopMoney{amount currencyCode}}

          originalTotalPriceSet  {shopMoney{amount currencyCode}}

          refundDiscrepancySet  {shopMoney{amount currencyCode}}

          subtotalPriceSet  {shopMoney{amount currencyCode}}

          totalDiscountsSet  {shopMoney{amount currencyCode}}
          totalPriceSet  {shopMoney{amount currencyCode}}
          totalReceivedSet  {shopMoney{amount currencyCode}}
          totalRefundedSet  {shopMoney{amount currencyCode}}
          totalRefundedShippingSet  {shopMoney{amount currencyCode}}
          totalShippingPriceSet  {shopMoney{amount currencyCode}}
          totalTaxSet  {shopMoney{amount currencyCode}}

          lineItems(first: #{line_items_per_page()}) {
            nodes {
              id
              name
              sku
              quantity
              currentQuantity
              refundableQuantity
              unfulfilledQuantity
              nonFulfillableQuantity

              originalUnitPriceSet {shopMoney{amount currencyCode}}
              discountedUnitPriceSet {shopMoney{amount currencyCode}}
            }
            pageInfo {
              hasNextPage
              endCursor
              hasNextPage
              startCursor
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasNextPage
          startCursor
        }
      }
    }
    """

  defp line_items_request_body(order_id, cursor),
    do: """
    query {
      orders(first: 1, query: "id:#{order_id}") {
        nodes {
          id
          name
          createdAt
    		customer {id}
          meta_tag: metafield(namespace: "custom", key: "order_tag") {value}

          lineItems(first: #{line_items_per_fetch()}, after: #{cursor |> parse_cursor()}) {
            nodes {
              id
              name
              sku
              quantity
              currentQuantity
              refundableQuantity
              unfulfilledQuantity
              nonFulfillableQuantity

              originalUnitPriceSet {shopMoney{amount currencyCode}}
              discountedUnitPriceSet {shopMoney{amount currencyCode}}
            }
            pageInfo {
              hasNextPage
              endCursor
              hasNextPage
              startCursor
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasNextPage
          startCursor
        }
      }
    }
    """

  defp paged_refunds_request_body(query, cursor, reverse?),
    do: """
    query {
      orders(query: "#{query}", #{refund_page_options(cursor, reverse?)}) {
        nodes {
          id
          name
          createdAt
          refunds {id}
          customer {id}
        }
        pageInfo {
          hasNextPage
          endCursor
          hasNextPage
          startCursor
        }
      }
    }
    """

  defp paged_refund_body(refund_id, cursor),
    do: """
    query {
      refund(id: "gid://shopify/Refund/#{refund_id}") {
        id
        note
        order {id name}
        createdAt
        updatedAt
        refundLineItems(#{refund_page_options(cursor, false)}) {
          nodes {
           lineItem {id sku name}
           priceSet {presentmentMoney{amount currencyCode}}
           quantity
           restockType
           restocked
          }
          pageInfo {
           hasNextPage
           endCursor
           hasNextPage
           startCursor
          }
        }
        totalRefundedSet {shopMoney{amount currencyCode}}
      }
    }

    """

  def fetch_query_to_page(query_string), do: fetch_query_to_page(query_string, 0)

  defp fetch_query_to_page(_query_string, @fetch_retry_limit),
    do: {:error, "Too many attempts to retry query."}

  defp fetch_query_to_page(query_string, attempts) do
    case fetch_query(query_string) do
      {:ok, data} ->
        Page.from_api(data)

      {:error, _} = _error ->
        fetch_query_to_page(query_string, attempts + 1)
    end
  end

  @spec fetch_query(String.t()) :: {:ok, any()} | {:error, any()}
  def fetch_query(query_string), do: fetch_query(query_string, 0)

  defp fetch_query(_query_string, @fetch_retry_limit),
    do: {:error, "Too many attempts to retry query."}

  defp fetch_query(query_string, attempts) do
    case Neuron.query(query_string) do
      {:ok, %{body: %{"data" => data}}} ->
        {:ok, data}

      {:error, _} = _error ->
        fetch_query(query_string, attempts + 1)

      {:ok, response} ->
        # Likely throttled, wait 1 second and try again
        :telemetry.execute([:admin, :integrations, :shopify, :loader, :unhandled_response], %{
          query_string: query_string,
          response: response,
          attempts: attempts
        })

        :timer.sleep(1000)
        fetch_query(query_string, attempts + 1)
    end
  end
end
