defmodule Admin.Integrations.Shopify.Intermediary.Order do
  @moduledoc """
  This module is an intermediary between the Shopify GraphQL API and the Admin Ash models.
  """
  alias Admin.Integrations.Shopify.Intermediary
  alias Admin.Integrations.Shopify.Loader

  defstruct [
    :id,
    :name,
    :line_items,
    :tags,
    :created_at,
    :meta_tag,
    :refunds,
    :customer_id,
    :discount_codes,

    # Pricing
    ## Current
    :current_subtotal_price_set,
    :current_total_discounts_set,
    :current_total_duties_set,
    :current_total_price_set,
    :current_total_tax_set,

    ## Statements
    :net_payment_set,
    :refund_discrepancy_set,

    ## Original
    :original_total_duties_set,
    :original_total_price_set,
    :subtotal_price_set,

    ## Totals
    :total_capturable_set,
    :total_discounts_set,
    :total_outstanding_set,
    :total_price_set,
    :total_received_set,
    :total_refunded_set,
    :total_refunded_shipping_set,
    :total_shipping_price_set,
    :total_tax_set,
    :total_tip_received_set
    # End Pricing
  ]

  def from_api(data) do
    %{
      "id" => id,
      "name" => name,
      "createdAt" => created_at
    } = data

    line_items = data |> Map.get("lineItems", [])
    meta_tag = data |> Map.get("meta_tag", nil)
    refunds = data |> Map.get("refunds", [])

    meta_tag = meta_tag |> parse_meta_tag()

    line_items =
      id
      |> maybe_fetch_all_line_items(line_items)

    created_at =
      case DateTime.from_iso8601(created_at) do
        {:ok, datetime, _} -> datetime
        {:error, _} -> raise "Could not parse created_at: #{inspect(created_at)}"
      end

    %__MODULE__{
      id: id,
      name: name,
      line_items: line_items,
      meta_tag: meta_tag,
      created_at: created_at,
      refunds: refunds |> Enum.map(&Intermediary.Refund.from_api/1),
      customer_id: data |> _get_customer_id(),

      discount_codes: data |> get_discount_codes(),

      # Pricing
      ## Current
      current_subtotal_price_set: data |> get_money("currentSubtotalPriceSet"),
      current_total_discounts_set: data |> get_money("currentTotalDiscountsSet"),
      current_total_duties_set: data |> get_money("currentTotalDutiesSet"),
      current_total_price_set: data |> get_money("currentTotalPriceSet"),
      current_total_tax_set: data |> get_money("currentTotalTaxSet"),

      ## Statements
      net_payment_set: data |> get_money("netPaymentSet"),
      refund_discrepancy_set: data |> get_money("refundDiscrepancySet"),

      ## Original
      original_total_duties_set: data |> get_money("originalTotalDutiesSet"),
      original_total_price_set: data |> get_money("originalTotalPriceSet"),
      subtotal_price_set: data |> get_money("subtotalPriceSet"),

      ## Totals
      total_capturable_set: data |> get_money("totalCapturableSet"),
      total_discounts_set: data |> get_money("totalDiscountsSet"),
      total_outstanding_set: data |> get_money("totalOutstandingSet"),
      total_price_set: data |> get_money("totalPriceSet"),
      total_received_set: data |> get_money("totalReceivedSet"),
      total_refunded_set: data |> get_money("totalRefundedSet"),
      total_refunded_shipping_set: data |> get_money("totalRefundedShippingSet"),
      total_shipping_price_set: data |> get_money("totalShippingPriceSet"),
      total_tax_set: data |> get_money("totalTaxSet"),
      total_tip_received_set: data |> get_money("totalTipReceivedSet")
      # End Pricing
    }
  end

  defp _get_customer_id(%{"customer" => %{"id" => id}}), do: id
  defp _get_customer_id(_), do: nil

  def to_args(%__MODULE__{} = order) do
    main_args = %{
      order_id: order.name,
      shopify_id: order.id,
      created_at: order.created_at,
      source: "stream-import",
      customer_id: order.customer_id,
      meta_tag: order.meta_tag,
      discount_codes: order.discount_codes
    }

    order
    |> Map.from_struct()
    |> Map.merge(main_args)
    |> Map.drop(~w(id name refunds line_items tags)a)
    |> Enum.filter(fn {_, value} -> value != nil end)
    |> Enum.into(%{})
  end

  defp maybe_fetch_all_line_items(id, %{"pageInfo" => %{"hasNextPage" => true}} = _line_items) do
    Loader.fetch_all_line_items_for_order_id(id)
  end

  defp maybe_fetch_all_line_items(_id, %{"nodes" => nodes}),
    do: nodes |> Enum.map(&Intermediary.LineItem.from_api/1)

  defp maybe_fetch_all_line_items(_id, other_value), do: other_value

  defp get_money(order, field) do
    case Map.get(order, field) do
      nil ->
        nil

      %{"shopMoney" => %{"amount" => amount, "currencyCode" => "USD"}} ->
        amount

      %{"shopMoney" => %{"currencyCode" => code}} ->
        raise "unexpected currency code #{code} for order #{order["id"]}"

      _ ->
        raise "unexpected money format for order #{order["id"]}"
    end
  end

  def get_discount_codes(data) do
    #Convert discount list to a concatenated string
    data
    |> Map.get("discountCodes", [])
    |> Enum.join(", ")
  end

  defp parse_meta_tag(nil), do: nil

  defp parse_meta_tag(%{"value" => value}) when is_binary(value) do
    # We might have a string or a json encoded array of strings.
    value
    |> String.replace("[\"", "")
    |> String.replace("\"]", "")
  end
end
