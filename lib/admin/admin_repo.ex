defmodule Admin.AdminRepo do
  @moduledoc """
  This module is the primary repo for the Admin Suite.
  """
  use AshPostgres.Repo,
    otp_app: :admin,
    adapter: Ecto.Adapters.Postgres

  @impl true
  def installed_extensions do
    ["ash-functions", "pg_trgm", "citext"]
  end

  def all_tenants do
    for tenant <- Ash.read!(Admin.Integrations.Gorgias.Tenant) do
      "gorgias_#{tenant.schema_name}"
    end
  end
end
defmodule Admin.AdminRepo.Replica do
  @moduledoc """
  This module is the primary repo for the Admin Suite.
  """
  use AshPostgres.Repo,
    otp_app: :admin,
    adapter: Ecto.Adapters.Postgres

  @impl true
  def installed_extensions do
    ["ash-functions", "pg_trgm", "citext"]
  end

  def replica do
    __MODULE__
  end

  def all_tenants do
    for tenant <- Ash.read!(Admin.Integrations.Gorgias.Tenant) do
      "gorgias_#{tenant.schema_name}"
    end
  end
end
