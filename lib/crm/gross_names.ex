defmodule Crm.GrossNames do
  use Ecto.Schema
  import Ecto.Changeset

  @moduledoc """
  This module provides the schema for the GrossNames table in the CRM database.
  Used for tracking gross names received by project and source.
  """

  @primary_key false
  schema "grossnames" do
    field :GNID, :integer, primary_key: true
    field :projectid, :integer
    field :sourceid, :string
    field :grossnames, :integer
    field :description, :string

    # # Associations
    # belongs_to :project, Crm.Project,
    #   foreign_key: :projectid,
    #   references: :id,
    #   define_field: false
  end

  @doc """
  Changeset function for the GrossNames schema.
  """
  def changeset(gross_names, attrs) do
    gross_names
    |> cast(attrs, [:projectid, :sourceid, :grossnames, :description])
    |> validate_required([:projectid, :sourceid, :grossnames])
    |> validate_number(:grossnames, greater_than_or_equal_to: 0)
  end
end
