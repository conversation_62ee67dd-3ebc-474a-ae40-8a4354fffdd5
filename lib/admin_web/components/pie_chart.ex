defmodule AdminWeb.Components.PieChart do
  @moduledoc """
  A comprehensive chart component for LiveView using ECharts.

  This component provides various chart types and variations including:
  - Pie charts: Basic pie, donut, rose (nightingale), nested
  - Bar charts: Vertical bars, horizontal bars, stacked bars
  - Line charts: Basic lines, smooth lines, area charts
  - Scatter plots: Basic scatter, bubble charts
  - Charts with custom themes and animations

  ## Usage in LiveView

  ### Basic Pie Chart
  ```elixir
  <.pie_chart
    id="my-pie-chart"
    title="Sales Distribution"
    data={[
      %{name: "Product A", value: 335},
      %{name: "Product B", value: 310},
      %{name: "Product C", value: 234}
    ]}
  />
  ```

  ### Donut Chart
  ```elixir
  <.pie_chart
    id="donut-chart"
    title="Revenue Breakdown"
    variant="donut"
    data={@revenue_data}
    width={400}
    height={300}
  />
  ```

  ### Rose Chart (Nightingale)
  ```elixir
  <.pie_chart
    id="rose-chart"
    title="Performance Metrics"
    variant="rose"
    data={@performance_data}
    theme="walden"
  />
  ```

  ### Nested Pie Chart
  ```elixir
  <.pie_chart
    id="nested-chart"
    title="Hierarchical Data"
    variant="nested"
    data={@nested_data}
    inner_radius="30%"
    outer_radius="60%"
  />
  ```

  ### Bar Chart
  ```elixir
  <.pie_chart
    id="bar-chart"
    title="Sales by Month"
    variant="bar"
    data={@monthly_sales}
    x_axis_data={["Jan", "Feb", "Mar", "Apr", "May"]}
  />
  ```

  ### Line Chart
  ```elixir
  <.pie_chart
    id="line-chart"
    title="Temperature Trend"
    variant="line"
    data={@temperature_data}
    x_axis_data={@dates}
    smooth={true}
  />
  ```

  ### Area Chart
  ```elixir
  <.pie_chart
    id="area-chart"
    title="Revenue Growth"
    variant="area"
    data={@revenue_data}
    x_axis_data={@quarters}
    area_style={%{opacity: 0.3}}
  />
  ```

  ## LiveView Integration

  To update chart data dynamically in your LiveView:

  ```elixir
  # In your LiveView module
  def handle_event("update_chart", _params, socket) do
    new_data = [
      %{name: "Updated A", value: 400},
      %{name: "Updated B", value: 300}
    ]

    # Push event to update the chart
    {:noreply, push_event(socket, "chart-update", %{
      chart_id: "my-pie-chart",
      data: new_data
    })}
  end
  ```

  ## Data Format

  Data should be provided as a list of maps with `name` and `value` keys:
  ```elixir
  [
    %{name: "Category 1", value: 100},
    %{name: "Category 2", value: 200},
    %{name: "Category 3", value: 150}
  ]
  ```

  For nested charts, provide additional `children` key:
  ```elixir
  [
    %{name: "Parent 1", value: 100, children: [
      %{name: "Child 1.1", value: 60},
      %{name: "Child 1.2", value: 40}
    ]},
    %{name: "Parent 2", value: 200}
  ]
  ```
  """

  use Phoenix.Component
  import AdminWeb.GadStyles.Components.Spinner

  @doc """
  Renders a pie chart with various customization options.

  ## Attributes

  * `id` - Required. Unique identifier for the chart
  * `title` - Chart title (optional)
  * `subtitle` - Chart subtitle (optional)
  * `data` - Required. List of data points with name and value
  * `variant` - Chart variant: "pie", "donut", "rose", "nested", "bar", "line", "area", "scatter" (default: "pie")
  * `theme` - Chart theme: "default", "walden", "dark" (default: "default")
  * `width` - Chart width in pixels (default: 400)
  * `height` - Chart height in pixels (default: 300)
  * `radius` - Chart radius, can be percentage or pixel value (default: "50%")
  * `inner_radius` - Inner radius for donut charts (default: "0%")
  * `outer_radius` - Outer radius for nested charts (default: "80%")
  * `center_x` - Horizontal center position (default: "50%")
  * `center_y` - Vertical center position (default: "50%")
  * `show_legend` - Whether to show legend (default: true)
  * `legend_position` - Legend position: "top", "bottom", "left", "right" (default: "right")
  * `show_labels` - Whether to show data labels (default: true)
  * `label_position` - Label position: "outside", "inside", "center" (default: "outside")
  * `animation` - Enable animations (default: true)
  * `loading` - Show loading state (default: false)
  * `class` - Additional CSS classes
  * `x_axis_data` - Category labels for x-axis (for bar/line charts)
  * `y_axis_type` - Y-axis type: "value", "category", "time", "log" (default: "value")
  * `x_axis_type` - X-axis type: "value", "category", "time", "log" (default: "category")
  * `stack` - Stack name for stacked charts (default: nil)
  * `smooth` - Enable smooth curves for line charts (default: false)
  * `area_style` - Area fill style for area charts (default: nil)
  * `show_grid` - Show grid lines (default: true)
  * `bar_width` - Width of bars for bar charts (default: nil)
  """
  attr :id, :string, required: true
  attr :title, :string, default: nil
  attr :subtitle, :string, default: nil
  attr :data, :list, required: true
  attr :variant, :string, default: "pie", values: ~w(pie donut rose nested bar line area scatter)
  attr :theme, :string, default: "default", values: ~w(default walden dark)
  attr :width, :integer, default: 400
  attr :height, :integer, default: 300
  attr :radius, :string, default: "50%"
  attr :inner_radius, :string, default: "0%"
  attr :outer_radius, :string, default: "80%"
  attr :center_x, :string, default: "50%"
  attr :center_y, :string, default: "50%"
  attr :show_legend, :boolean, default: true
  attr :legend_position, :string, default: "right", values: ~w(top bottom left right)
  attr :show_labels, :boolean, default: true
  attr :label_position, :string, default: "outside", values: ~w(outside inside center)
  attr :animation, :boolean, default: true
  attr :loading, :boolean, default: false
  attr :class, :string, default: ""
  # Bar/Line chart specific attributes
  attr :x_axis_data, :list, default: []
  attr :y_axis_type, :string, default: "value", values: ~w(value category time log)
  attr :x_axis_type, :string, default: "category", values: ~w(value category time log)
  attr :stack, :string, default: nil
  attr :smooth, :boolean, default: false
  attr :area_style, :map, default: nil
  attr :show_grid, :boolean, default: true
  attr :bar_width, :string, default: nil

  def pie_chart(assigns) do
    assigns = assign(assigns, :chart_config, build_chart_config(assigns))

    ~H"""
    <div class={["pie-chart-container", @class]}>
      <div
        id={@id}
        phx-hook="EChartHook"
        phx-update="ignore"
        data-ref-id={@id}
        data-template={encode_config(@chart_config)}
        style={"width: #{@width}px; height: #{@height}px;"}
        class="pie-chart"
      >
        <div :if={@loading} class="flex items-center justify-center h-full">
          <.spinner type="bars" color="primary" />
        </div>
      </div>
    </div>
    """
  end

  # Functions for building chart configuration

  @doc false
  def build_chart_config(assigns) do
    base_config = %{
      animation: assigns.animation,
      series: [build_series_config(assigns)]
    }

    base_config
    |> maybe_add_title(assigns)
    |> maybe_add_legend(assigns)
    |> maybe_add_tooltip(assigns)
    |> maybe_add_theme(assigns.theme)
    |> maybe_add_axis(assigns)
  end

  defp build_series_config(assigns) do
    chart_type = get_chart_type(assigns.variant)

    base_series = %{
      name: assigns.title || "Data",
      type: chart_type,
      emphasis: %{
        itemStyle: %{
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        }
      }
    }

    base_series
    |> add_chart_data(assigns, chart_type)
    |> apply_variant_config(assigns)
    |> maybe_add_labels(assigns)
  end

  defp get_chart_type(variant) do
    case variant do
      v when v in ~w(pie donut rose nested) -> "pie"
      "bar" -> "bar"
      "line" -> "line"
      "area" -> "line"
      "scatter" -> "scatter"
      _ -> "pie"
    end
  end

  defp add_chart_data(series, assigns, chart_type) do
    case chart_type do
      "pie" ->
        series
        |> Map.put(:data, format_data(assigns.data))
        |> Map.put(:center, [assigns.center_x, assigns.center_y])

      type when type in ["bar", "line", "scatter"] ->
        series
        |> Map.put(:data, format_chart_data(assigns.data, type))
        |> maybe_add_stack(assigns)
        |> maybe_add_smooth(assigns)
        |> maybe_add_area_style(assigns)
        |> maybe_add_bar_width(assigns)

      _ ->
        Map.put(series, :data, format_data(assigns.data))
    end
  end

  defp apply_variant_config(series, %{variant: "donut"} = assigns) do
    Map.merge(series, %{
      radius: [assigns.inner_radius, assigns.radius],
      avoidLabelOverlap: false
    })
  end

  defp apply_variant_config(series, %{variant: "rose"}) do
    Map.merge(series, %{
      roseType: "radius",
      itemStyle: %{
        borderRadius: 8
      }
    })
  end

  defp apply_variant_config(series, %{variant: "nested"} = assigns) do
    Map.merge(series, %{
      radius: [assigns.inner_radius, assigns.outer_radius]
    })
  end

  defp apply_variant_config(series, %{variant: "pie"} = assigns) do
    Map.put(series, :radius, assigns.radius)
  end

  defp apply_variant_config(series, %{variant: "bar"}) do
    series
  end

  defp apply_variant_config(series, %{variant: "line"}) do
    series
  end

  defp apply_variant_config(series, %{variant: "area"} = assigns) do
    area_style = assigns.area_style || %{opacity: 0.6}
    Map.put(series, :areaStyle, area_style)
  end

  defp apply_variant_config(series, %{variant: "scatter"}) do
    Map.merge(series, %{
      symbolSize: 8,
      itemStyle: %{
        opacity: 0.8
      }
    })
  end

  defp apply_variant_config(series, _assigns), do: series

  defp maybe_add_labels(series, %{show_labels: false}) do
    Map.put(series, :label, %{show: false})
  end

  defp maybe_add_labels(series, %{show_labels: true, label_position: position}) do
    label_config = case position do
      "inside" -> %{position: "inside"}
      "center" -> %{position: "center"}
      "outside" -> %{position: "outside"}
    end

    Map.put(series, :label, label_config)
  end

  defp maybe_add_stack(series, %{stack: nil}), do: series
  defp maybe_add_stack(series, %{stack: stack}) when is_binary(stack) do
    Map.put(series, :stack, stack)
  end

  defp maybe_add_smooth(series, %{smooth: false}), do: series
  defp maybe_add_smooth(series, %{smooth: true}) do
    Map.put(series, :smooth, true)
  end

  defp maybe_add_area_style(series, %{area_style: nil}), do: series
  defp maybe_add_area_style(series, %{area_style: area_style}) when is_map(area_style) do
    series =
      Map.put(series, :areaStyle, %{
        color: area_style.color,
        opacity: area_style.opacity,
        shadowBlur: area_style.shadowBlur,
        shadowColor: area_style.shadowColor,
        shadowOffsetX: area_style.shadowOffsetX,
        shadowOffsetY: area_style.shadowOffsetY
      })

    if(area_style.shadow?, do: Map.put(series, :areaStyle,
      %{shadowBlur: area_style.shadowBlur,
        shadowColor: area_style.shadowColor,
        shadowOffsetX: area_style.shadowOffsetX,
        shadowOffsetY: area_style.shadowOffsetY}),
      else: series
    )
  end

  defp maybe_add_bar_width(series, %{bar_width: nil}), do: series
  defp maybe_add_bar_width(series, %{bar_width: width}) when is_binary(width) do
    Map.put(series, :barWidth, width)
  end

  defp maybe_add_title(config, %{title: nil}), do: config
  defp maybe_add_title(config, assigns) do
    title_config = %{
      text: assigns.title,
      left: "center",
      top: "5%"
    }

    title_config = if assigns.subtitle do
      Map.put(title_config, :subtext, assigns.subtitle)
    else
      title_config
    end

    Map.put(config, :title, title_config)
  end

  defp maybe_add_legend(config, %{show_legend: false}), do: config
  defp maybe_add_legend(config, assigns) do
    legend_config = case assigns.legend_position do
      "top" -> %{orient: "horizontal", top: "top"}
      "bottom" -> %{orient: "horizontal", bottom: "bottom"}
      "left" -> %{orient: "vertical", left: "left"}
      "right" -> %{orient: "vertical", right: "right"}
    end

    Map.put(config, :legend, legend_config)
  end

  defp maybe_add_tooltip(config, %{type: "pie"} = assigns) do
    Map.put(config, :tooltip, %{
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    })
  end
  defp maybe_add_tooltip(config, %{variant: variant} = assigns) when variant in ~w(bar line area scatter) do
    tooltip_config = case variant do
      "bar" -> %{
        trigger: "axis",
        axisPointer: %{type: "shadow"}
      }
      v when v in ~w(line area) -> %{
        trigger: "axis",
        axisPointer: %{
          type: "cross",
          label: %{backgroundColor: "#6a7985"}
        }
      }
      "scatter" -> %{
        trigger: "item",
        formatter: "{a} <br/>{b}: {c}"
      }
    end

    Map.put(config, :tooltip, tooltip_config)
  end

  defp maybe_add_tooltip(config, _assigns) do
    Map.put(config, :tooltip, %{
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    })
  end

  defp maybe_add_theme(config, "default"), do: config
  defp maybe_add_theme(config, theme) do
    # Theme colors would be loaded from theme files
    theme_colors = get_theme_colors(theme)
    Map.put(config, :color, theme_colors)
  end

  defp maybe_add_axis(config, %{type: "pie"} = assigns), do: config
  defp maybe_add_axis(config, %{type: "bar"} = assigns) do
    Map.put(config, :xAxis, %{
      type: "category",
      data: assigns.x_axis_data,
      name: assigns.x_axis_title, # Set the title for the X-axis
      nameLocation: "middle", # Position the title (start, middle, end)
      nameGap: 25, # Gap between the title and the axis line
      nameTextStyle: %{ # Style for the title text
          color: "#333",
          fontSize: 12,
          fontWeight: "bold"
      }
    })
    |> Map.put(:yAxis, %{
      type: "value"
    })
  end

  defp get_theme_colors("walden") do
    ["#3fb1e3", "#6be6c1", "#626c91", "#a0a7e6", "#c4ebad", "#96dee8"]
  end

  defp get_theme_colors("dark") do
    ["#dd6b66", "#759aa0", "#e69d87", "#8dc1a9", "#ea7e53", "#eedd78"]
  end

  defp get_theme_colors(_), do: nil

  defp maybe_add_axis(config, assigns) do
    chart_type = get_chart_type(assigns.variant)

    case chart_type do
      "pie" -> config
      _ ->
        x_axis = %{
          type: assigns.x_axis_type,
          splitLine: %{show: assigns.show_grid}
        }

        # Add data for category axis
        x_axis = if assigns.x_axis_type == "category" and length(assigns.x_axis_data) > 0 do
          Map.put(x_axis, :data, assigns.x_axis_data)
        else
          x_axis
        end

        y_axis = %{
          type: assigns.y_axis_type,
          splitLine: %{show: assigns.show_grid}
        }

        config
        |> Map.put(:xAxis, x_axis)
        |> Map.put(:yAxis, y_axis)
        |> maybe_add_grid(assigns)
    end
  end

  defp maybe_add_grid(config, %{show_grid: false}), do: config
  defp maybe_add_grid(config, %{show_grid: true}) do
    Map.put(config, :grid, %{
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    })
  end

  @doc false
  def format_data(data) when is_list(data) do
    Enum.map(data, &format_data_item/1)
  end

  defp format_data_item(%{name: name, value: value} = item) do
    base_item = %{name: name, value: value}

    # Add any additional properties like itemStyle, emphasis, etc.
    item
    |> Map.drop([:name, :value])
    |> Enum.reduce(base_item, fn {key, val}, acc ->
      Map.put(acc, key, val)
    end)
  end

  defp format_chart_data(data, chart_type) when is_list(data) do
    case chart_type do
      type when type in ["bar", "line"] ->
        # For bar/line charts, extract just the values if data has name/value structure
        Enum.map(data, fn
          %{value: value} -> value
          %{name: _name, value: value} -> value
          value when is_number(value) -> value
          _ -> 0
        end)

      "scatter" ->
        # For scatter plots, expect [x, y] coordinate pairs
        Enum.map(data, fn
          %{x: x, y: y} -> [x, y]
          %{value: [x, y]} -> [x, y]
          [x, y] -> [x, y]
          %{name: _name, value: value} -> [0, value]
          _ -> [0, 0]
        end)

      _ ->
        format_data(data)
    end
  end

  defp encode_config(config) do
    config
    |> Jason.encode!()
    |> Base.encode64()
  end
end
