<nav class="flex-1 px-2 pb-4">
  <ul class="flex flex-1 flex-col space-y-1">
    <.menu_item :if={is_nil(@current_user)} href={~p"/sign_in"}>
      <div>To see your menu</div>
      <div class="font-bold underline">Sign in</div>
    </.menu_item>
    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :internal_dashboard, @current_user)}
      name="AppInt"
      title="Application Internals"
    >
      <:icon>
        <Heroicons.adjustments_horizontal outline class="h-5 w-5" />
      </:icon>
      <.menu_item href={~p"/dashboard"}>
        Live Dashboard
      </.menu_item>
      <.menu_item href={~p"/jobs"}>
        Job Scheduler
      </.menu_item>
      <.menu_item href={~p"/jobs/oban"}>
        Oban Job Panel
      </.menu_item>
    </.menu_group_toggle>

    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :org, @current_user)}
      name="OrgTog"
      title="Org"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/org")}
    >
      <:icon>
        <Heroicons.user_group outline class="h-5 w-5" />
      </:icon>
      <.menu_item href={~p"/org/business_entities/"}>
        Business Entities
      </.menu_item>
      <.menu_item href={~p"/org/centers/"}>
        Centers
      </.menu_item>
      <.menu_item href={~p"/org/teams/"}>
        Teams
      </.menu_item>
      <.menu_item href={~p"/org/users/"}>
        Users
      </.menu_item>
    </.menu_group_toggle>

    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :messaging, @current_user)}
      name="SMSTog"
      title="Messaging"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/messaging")}
    >
      <:icon>
        <Heroicons.chat_bubble_left outline class="h-5 w-5" />
      </:icon>
      <.menu_item href={~p"/messaging/outbound_text_batches"}>
        SMS Messages Batches (Outbound)
      </.menu_item>
      <.menu_item href={~p"/messaging/qc"}>
        SMS QC
      </.menu_item>
      <.menu_group_toggle name="SmsLogs" title="SMS Logs">
        <li>
          <.menu_item href={~p"/messaging/inbound_sms"}>
            SMS Messages (Inbound)
          </.menu_item>
        </li>
        <li>
          <.menu_item href={~p"/messaging/outbound_text_messages"}>
            SMS Message Log (Outbound)
          </.menu_item>
        </li>
      </.menu_group_toggle>
      <.menu_group
        :if={Application.get_env(:admin, :environment) in [:dev, :test]}
        name="DevTools"
        title="Development Tools"
      >
        <li>
          <.menu_item href={~p"/messaging/intercepted_sms"}>
            Intercepted SMS Messages
          </.menu_item>
        </li>
      </.menu_group>
    </.menu_group_toggle>

    <.menu_group_toggle
      :if={
        Bodyguard.permit?(MenuBar, :files, @current_user) ||
          Bodyguard.permit?(MenuBar, :setups, @current_user) ||
          Bodyguard.permit?(MenuBar, :campaign, @current_user)
      }
      name="CRMTog"
      title="CRM"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/crm")}
    >
      <:icon>
        <Heroicons.users outline class="h-5 w-5" />
      </:icon>

      <.menu_group
        :if={Bodyguard.permit?(MenuBar, :setups, @current_user)}
        name="SetupTog"
        title="Setups"
      >
        <li>
          <.menu_item href={~p"/crm/setups"}>
            List Setups
          </.menu_item>
        </li>
        <li>
          <.menu_item href={~p"/crm/setups/new"}>
            New Setup
          </.menu_item>
        </li>
      </.menu_group>

      <.menu_group
        :if={Bodyguard.permit?(MenuBar, :files, @current_user)}
        name="LeadFilesTog"
        title="Lead Files"
      >
        <li>
          <.menu_item href={~p"/crm/lead_files"}>
            List Lead Files
          </.menu_item>
        </li>
      </.menu_group>

      <li :if={Bodyguard.permit?(MenuBar, :setups, @current_user)}>
        <.menu_item href={~p"/crm/dncs"}>
          DNC Export
        </.menu_item>
      </li>

       <.menu_group
        :if={Bodyguard.permit?(MenuBar, :campaign, @current_user)}
        name="CampTog"
        title="Campaign Management"
      >
        <li>
          <.menu_item href={~p"/crm/campaign_tools/group_leads_reset"}>
            Group Leads Reset
          </.menu_item>
        </li>
        <li>
          <.menu_item href={~p"/crm/campaign_tools/contactability"}>
            Contactability Report
          </.menu_item>
        </li>
      </.menu_group>

      <.menu_group_toggle
        :if={Bodyguard.permit?(MenuBar, :setups, @current_user)}
        name="LegacySetupTog"
        title="Legacy Setup Tools"
      >
        <li>
          <.menu_item href={~p"/crm/setups/load_legacy"}>
            Legacy Lead Loader
          </.menu_item>
        </li>
        <li>
          <.menu_item href={~p"/crm/setups/new_legacy"}>
            Legacy Project Creator
          </.menu_item>
        </li>
        <li>
          <.menu_item href={~p"/crm/files/upload"}>
            Legacy File Upload
          </.menu_item>
        </li>
      </.menu_group_toggle>
    </.menu_group_toggle>

    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :setups, @current_user)}
      name="ReportTog"
      title="Reports"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/reports")}
    >
      <:icon>
        <Heroicons.presentation_chart_line outline class="h-5 w-5" />
      </:icon>

    <%!-- <.menu_group
        :if={Bodyguard.permit?(MenuBar, :setups, @current_user)}
        
        name="ReportTog"
        title="ADR Reports"
        active?={@request_path == "/" || String.starts_with?(@request_path, "/reports/adr")}
      > --%>
        <li>
          <.menu_item href={~p"/reports/adr"}>
            ADR Reports
          </.menu_item>
        </li>
      <%!-- </.menu_group> --%>
    </.menu_group_toggle>

    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :mailers, @current_user)}
      name="MailerTog"
      title="Mailers"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/transcription")}
    >
      <:icon>
        <Heroicons.inbox_stack outline class="h-5 w-5" />
      </:icon>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :mailers, @current_user, section: :admin)}
        href={~p"/transcription/admin"}
      >
        Admin
      </.menu_item>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :mailers, @current_user, section: :forms)}
        href={~p"/transcription/forms"}
      >
        Forms
      </.menu_item>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :mailers, @current_user, section: :list)}
        href={~p"/transcription/mailers"}
      >
        List
      </.menu_item>
      <.menu_item href={~p"/transcription/transcribe"}>
        Enter (Transcribe)
      </.menu_item>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :mailers, @current_user, section: :audit)}
        href={~p"/transcription/review/"}
      >
        Audit (QC)
      </.menu_item>
    </.menu_group_toggle>
    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :time_tracking, @current_user)}
      name="TimeTog"
      title="Time Tracking"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/time_tracking")}
    >
      <:icon>
        <Heroicons.clock outline class="h-5 w-5" />
      </:icon>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :time_tracking_section, @current_user, section: :tasks)}
        href={~p"/time_tracking/tasks"}
      >
        Tasks
      </.menu_item>
      <.menu_item href={~p"/time_tracking/punches"}>
        Punches
      </.menu_item>
    </.menu_group_toggle>

    <.menu_group_toggle
      :if={Bodyguard.permit?(MenuBar, :cid_lookup, @current_user)}
      name="CallerTog"
      title="Caller IDs"
      active?={@request_path == "/" || String.starts_with?(@request_path, "/tools")}
    >
      <:icon>
        <Heroicons.table_cells outline class="h-5 w-5" />
      </:icon>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :use_lookup, @current_user, section: :cid_tools)}
        href={~p"/tools/cid_used_lookup"}
      >
        CID Used Lookup Tool
      </.menu_item>
      <.menu_item
        :if={Bodyguard.permit?(MenuBar, :display_lookup, @current_user, section: :cid_tools)}
        href={~p"/tools/cid_display"}
      >
        Caller ID Display Lookup Tool
      </.menu_item>
    </.menu_group_toggle>
  </ul>
</nav>
