defmodule AdminWeb.CoreComponents do
  @moduledoc """
  Provides core UI components.

  At first glance, this module may seem daunting, but its goal is to provide
  core building blocks for your application, such as modals, tables, and
  forms. The components consist mostly of markup and are well-documented
  with doc strings and declarative assigns. You may customize and style
  them in any way you want, based on your application growth and needs.

  The default components use Tailwind CSS, a utility-first CSS framework.
  See the [Tailwind CSS documentation](https://tailwindcss.com) to learn
  how to customize them or feel free to swap in another framework altogether.

  Icons are provided by [heroicons](https://heroicons.com). See `icon/1` for usage.
  """
  use Phoenix.Component

  alias AdminWeb.FlopHelpers
  alias Phoenix.LiveView.JS
  import AdminWeb.ComponentHelpers
  import AdminWeb.Gettext
  # import AdminWeb.SetupLive.Components

  @doc """
  Renders an internal link, that is one that may lead to another part of the app, but still is internal.

  ## Examples

      <.internal_link href="https://google.com/"/>
      <.internal_link href="https://google.com/" target="google"/>
  """
  attr :target, :string, default: "_"
  attr :href, :string, required: true
  attr :text, :string, default: nil
  attr :image, :string, default: ""

  def internal_link(assigns) do
    ~H"""
    <a href={@href} target={@target} class="flex whitespace-nowrap">
      <span class="pl-0 pr-1">{@text || @href}</span>
      <Heroicons.arrow_right_circle class="h-5 w-5" />
    </a>
    """
  end

  @doc """
  Renders a keyboard shortcut.

  It has 4 "kinds" of styling: default, info, warning, and error.

  Default is styled unasumingly, suitable for any purpose.
  All styles have a light and dark theme.

  The `text` attribute overrides the inner block, and is formatted the same.

  Optionally, you can add a `clarify` attribute, which will be displayed when hovering over the hotkey.

  If you wish to add a class, you can add to the span class by using the `class` attribute.

  ## Examples

      <.hotkey>CTRL</.hotkey>
      <.hotkey kind={:info} clarify="Shift+/>?</.hotkey>
      <.hotkey kind={:warning}>Return</.hotkey>
      <.hotkey kind={:error} clarify="Shift+/>?" text="Esc"/>
  """

  attr :text, :string, default: nil, doc: "the text to display, overrides inner block"
  attr :clarify, :string, default: nil, doc: "the text to display when hovering over the hotkey"

  attr :kind, :atom,
    values: [:default, :info, :warning, :error],
    default: :default,
    doc: "used for styling and flash lookup"

  attr :class, :string, default: nil, doc: "the optional class for the keyboard shortcut"
  attr :rest, :global, doc: "the arbitrary HTML attributes to add to the keyboard shortcut"

  slot :inner_block,
    doc:
      "the optional inner block that renders the keyboard shortcut. Overridden by text attribute"

  def hotkey(assigns) do
    ~H"""
    <span class="group flex items-center font-medium font-sans drop-shadow-md" {@rest}>
      <span class={[
        "px-1 py-0.5 rounded-sm leading-none text-sm text-no",
        "border-0 border-b-3",
        hotkey_bg(@kind),
        hotkey_text(@kind),
        @class || ""
      ]}>
        <span :if={@clarify} class="hidden group-hover:block">({@clarify})</span>
        {@text || render_slot(@inner_block)}
      </span>
    </span>
    """
  end

  defp hotkey_bg(:default),
    do:
      "bg-zinc-300 dark:bg-zinc-600" <>
        " " <>
        "border-zinc-400 dark:border-zinc-700"

  defp hotkey_bg(:info),
    do:
      "bg-emerald-300 dark:bg-emerald-600" <>
        " " <>
        "border-emerald-400 dark:border-emerald-700"

  defp hotkey_bg(:warning),
    do:
      "bg-amber-300 dark:bg-amber-600" <>
        " " <>
        "border-amber-400 dark:border-amber-700"

  defp hotkey_bg(:error),
    do:
      "bg-rose-300 dark:bg-rose-600" <>
        " " <>
        "border-rose-400 dark:border-rose-700"

  defp hotkey_text(:default), do: "text-gray-700 dark:text-gray-100"
  defp hotkey_text(:info), do: "text-emerald-700 dark:text-emerald-100"
  defp hotkey_text(:warning), do: "text-amber-700 dark:text-amber-100"
  defp hotkey_text(:error), do: "text-rose-700 dark:text-red-50"

  @doc """
  Renders an external link

  ## Examples

      <.external_link href="https://google.com/"/>
      <.external_link href="https://google.com/" target="google"/>
  """
  attr :target, :string, default: "_"
  attr :href, :string, required: true
  attr :text, :string, default: nil
  attr :image, :string, default: ""

  def external_link(assigns) do
    ~H"""
    <a href={@href} target={@target} class="flex whitespace-nowrap content-center">
      <span class="pl-0 pr-1 align-middle items-center">{@text || @href}</span>
      <%= if @image == "" do %>
        <Heroicons.arrow_top_right_on_square class="h-5 w-5" />
      <% else %>
        <img src={@image} alt="Jira Logo" class="h-4 w-4 place-self-center" />
      <% end %>
    </a>
    """
  end

  @doc """
  Renders a user block

  ## Examples
    <.user user={user} />
  """
  attr :size, :integer, default: 10
  attr :user, Admin.Accounts.User

  def user(%{user: nil} = assigns) do
    ~H"""
    <div class="flex items-center">
      <div class={"h-#{@size} w-#{@size} shrink-0"}></div>
      <div class="ml-4">
        <div class="font-medium text-gray-900">Unassigned</div>
        <div class="text-gray-500"></div>
      </div>
    </div>
    """
  end

  def user(assigns) do
    ~H"""
    <div class="flex items-center">
      <div class={"h-#{@size} w-#{@size} shrink-0"}>
        <img class={"h-#{@size} w-#{@size} rounded-full"} src={@user.avatar_url} alt="" />
      </div>
      <div class="ml-4">
        <div class="font-medium text-gray-900">{@user.name}</div>
        <div class="text-gray-500">{@user.email}</div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a datetime from a NaiveDateTime

  ## Examples
    <.datetime id="some-item-id-inserted-at" dt={NaiveDateTime.local_now()} />
  """
  attr :id, :string, required: true
  attr :dt, NaiveDateTime
  attr :short?, :boolean, default: false
  attr :default, :string, default: "", doc: "When dt cannot be parsed, this value is used"
  attr :utc, :boolean, default: false

  def datetime(assigns) do
    ~H"""
    <time id={@id} phx-hook="LocalTime" data-utc={@utc} datetime={@dt}>
      {parse_datetime(@dt, @short?) || @default}
    </time>
    """
  end

  defp parse_datetime(%NaiveDateTime{} = dt, true) do
    dt
    |> Timex.format("{YYYY}-{0M}-{0D}")
    |> maybe_handle_blank()
  end

  defp parse_datetime(%NaiveDateTime{} = dt, false) do
    dt
    |> Timex.format("{YYYY}-{0M}-{0D} {h12}:{m}:{s} {AM}")
    |> maybe_handle_blank()
  end

  defp parse_datetime(_, _), do: nil

  defp maybe_handle_blank({:ok, str}), do: str
  defp maybe_handle_blank({:error, _}), do: nil

  @doc """
  Renders a modal.

  ## Examples

      <.modal id="confirm-modal">
        Are you sure?
        <:confirm>OK</:confirm>
        <:cancel>Cancel</:cancel>
      </.modal>

  JS commands may be passed to the `:on_cancel` and `on_confirm` attributes
  for the caller to react to each button press, for example:

      <.modal id="confirm" on_confirm={JS.push("delete")} on_cancel={JS.navigate(~p"/posts")}>
        Are you sure you?
        <:confirm>OK</:confirm>
        <:cancel>Cancel</:cancel>
      </.modal>
  """
  attr :id, :string, required: true
  attr :show, :boolean, default: false
  attr :on_cancel, JS, default: %JS{}
  attr :on_confirm, JS, default: %JS{}

  slot :inner_block, required: true
  slot :title
  slot :subtitle
  slot :confirm
  slot :cancel

  def modal(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && show_modal(@id)}
      phx-remove={hide_modal(@id)}
      class="relative z-50 hidden"
    >
      <div id={"#{@id}-bg"} class="fixed inset-0 bg-zinc-50/90 transition-opacity" aria-hidden="true" />
      <div
        class="fixed inset-0 overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div class="flex min-h-full items-center justify-center">
          <div class="w-full max-w-3xl p-4 sm:p-6 lg:py-8">
            <.focus_wrap
              id={"#{@id}-container"}
              phx-mounted={@show && show_modal(@id)}
              phx-window-keydown={hide_modal(@on_cancel, @id)}
              phx-key="escape"
              phx-click-away={hide_modal(@on_cancel, @id)}
              class="hidden relative rounded-2xl bg-white p-14 shadow-lg shadow-zinc-700/10 ring-1 ring-zinc-700/10 transition"
            >
              <div class="absolute top-6 right-5">
                <button
                  phx-click={hide_modal(@on_cancel, @id)}
                  type="button"
                  class="-m-3 flex-none p-3 opacity-20 hover:opacity-40"
                  aria-label={gettext("close")}
                >
                  <Heroicons.x_mark solid class="w-5 h-5" />
                </button>
              </div>
              <div id={"#{@id}-content"}>
                <header :if={@title != []}>
                  <h1 id={"#{@id}-title"} class="text-lg font-semibold leading-8 text-zinc-800">
                    {render_slot(@title)}
                  </h1>
                  <p
                    :if={@subtitle != []}
                    id={"#{@id}-description"}
                    class="mt-2 text-sm leading-6 text-zinc-600"
                  >
                    {render_slot(@subtitle)}
                  </p>
                </header>
                {render_slot(@inner_block)}
                <div :if={@confirm != [] or @cancel != []} class="ml-6 mb-4 flex items-center gap-5">
                  <.button
                    :for={confirm <- @confirm}
                    id={"#{@id}-confirm"}
                    phx-click={@on_confirm}
                    phx-disable-with
                    class="py-2 px-3"
                  >
                    {render_slot(confirm)}
                  </.button>
                  <.link
                    :for={cancel <- @cancel}
                    phx-click={hide_modal(@on_cancel, @id)}
                    class="text-sm font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
                  >
                    {render_slot(cancel)}
                  </.link>
                </div>
              </div>
            </.focus_wrap>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders flash notices.

  ## Examples

      <.flash kind={:info} flash={@flash} />
      <.flash kind={:info} phx-mounted={show("#flash")}>Welcome Back!</.flash>
  """
  attr :id, :string, default: "flash", doc: "the optional id of flash container"
  attr :flash, :map, default: %{}, doc: "the map of flash messages to display"
  attr :title, :string, default: nil
  attr :kind, :atom, values: [:info, :warning, :error], doc: "used for styling and flash lookup"
  attr :autoshow, :boolean, default: true, doc: "whether to auto show the flash on mount"
  attr :close, :boolean, default: true, doc: "whether the flash can be closed"
  attr :rest, :global, doc: "the arbitrary HTML attributes to add to the flash container"

  slot :inner_block, doc: "the optional inner block that renders the flash message"

  def flash(assigns) do
    ~H"""
    <div
      :if={msg = render_slot(@inner_block) || Phoenix.Flash.get(@flash, @kind)}
      id={@id}
      phx-mounted={@autoshow && show("##{@id}")}
      phx-click={JS.push("lv:clear-flash", value: %{key: @kind}) |> hide("##{@id}")}
      role="alert"
      class={[
        "fixed hidden top-2 right-2 w-80 sm:w-96 z-40 rounded-lg p-3 shadow-md shadow-zinc-900/5 ring-1",
        @kind == :info && "bg-emerald-50 text-emerald-800 ring-emerald-500 fill-cyan-900",
        @kind == :warning && "bg-amber-50 text-amber-800 ring-amber-500 fill-amber-900",
        @kind == :error && "bg-rose-50 p-3 text-rose-900 shadow-md ring-rose-500 fill-rose-900"
      ]}
      {@rest}
    >
      <p :if={@title} class="flex items-center gap-1.5 text-[0.8125rem] font-semibold leading-6">
        <Heroicons.information_circle :if={@kind in [:info, :warning]} mini class="w-4 h-4" />
        <Heroicons.exclamation_circle :if={@kind == :error} mini class="w-4 h-4" />
        {@title}
      </p>
      <p class="mt-2 text-[0.8125rem] leading-5">{msg}</p>
      <button
        :if={@close}
        type="button"
        class="group absolute top-2 right-1 p-2"
        aria-label={gettext("close")}
      >
        <Heroicons.x_mark solid class="w-5 h-5 opacity-40 group-hover:opacity-70" />
      </button>
    </div>
    """
  end

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr :flash, :map, required: true, doc: "the map of flash messages"

  def flash_group(assigns) do
    ~H"""
    <.flash kind={:info} title="Success!" flash={@flash} />
    <.flash kind={:warning} title="Warning!" flash={@flash} />
    <.flash kind={:error} title="Error!" flash={@flash} />

    <.flash
      id="disconnected"
      kind={:error}
      title="We can't find the internet"
      close={false}
      autoshow={false}
      phx-disconnected={show("#disconnected")}
      phx-connected={hide("#disconnected")}
    >
      Attempting to reconnect <Heroicons.arrow_path class="ml-1 w-3 h-3 animate-spin" />
    </.flash>
    """
  end

  @doc """
  Renders a stylized alert.

  ## Examples
    <.alert kind={:info}>This is an alert!</.alert>
    <.alert kind={:warning}>This is an alert!</.alert>
    <.alert kind={:error}>This is an alert!</.alert>
    <.alert kind={:info}>This is an alert!</.alert>
  """
  attr :kind, :atom, values: [:info, :warning, :error], default: :info
  slot :title, doc: "the optional title of the alert"
  slot :inner_block, required: true

  def alert(%{kind: :info} = assigns) do
    assigns
    |> assign(kind: nil)
    |> assign(title_color: "text-blue-400")
    |> assign(text_color: "text-blue-400")
    |> assign(bg_color: "bg-blue-50")
    |> assign(accent_color: "border-blue-400")
    |> assign(icon: "information_circle")
    |> alert()
  end

  def alert(%{kind: :warning} = assigns) do
    assigns
    |> assign(kind: nil)
    |> assign(title_color: "text-yellow-800")
    |> assign(text_color: "text-yellow-700")
    |> assign(bg_color: "bg-yellow-50")
    |> assign(accent_color: "border-yellow-400")
    |> assign(icon: "information_circle")
    |> alert()
  end

  def alert(%{kind: :error} = assigns) do
    assigns
    |> assign(kind: nil)
    |> assign(title_color: "text-red-400")
    |> assign(text_color: "text-red-400")
    |> assign(bg_color: "bg-red-50")
    |> assign(accent_color: "border-red-400")
    |> assign(icon: "information_circle")
    |> alert()
  end

  def alert(assigns) do
    ~H"""
    <div class={[@accent_color, @bg_color, "border-l-4 p-4"]}>
      <div class="flex">
        <div class="shrink-0">
          <svg
            class={[@text_color, "h-5 w-5"]}
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fill-rule="evenodd"
              d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class={[@title_color, "text-sm font-medium"]}>
            {render_slot(@title) || "Notice"}
          </h3>
          <div class={[@text_color, "mt-2 text-sm"]}>
            <p>
              {render_slot(@inner_block)}
            </p>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a simple form.

  ## Examples

      <.simple_form for={@form} phx-change="validate" phx-submit="save">
        <.input field={@form[:email]} label="Email"/>
        <.input field={@form[:username]} label="Username" />
        <:actions>
          <.button>Save</.button>
        </:actions>
      </.simple_form>
  """
  attr :for, :any, required: true, doc: "the datastructure for the form"
  attr :as, :any, default: nil, doc: "the server side parameter to collect all input under"

  attr :rest, :global,
    include: ~w(id autocomplete name rel action enctype method novalidate target),
    doc: "the arbitrary HTML attributes to apply to the form tag"

  slot :inner_block, required: true
  slot :actions, doc: "the slot for form actions, such as a submit button"

  def simple_form(assigns) do
    ~H"""
    <.form :let={f} for={@for} as={@as} {@rest}>
      <div class="space-y-8 bg-white mt-2">
        {render_slot(@inner_block, f)}
        <div :for={action <- @actions} class="mt-2 flex items-center justify-between gap-6">
          {render_slot(action, f)}
        </div>
      </div>
    </.form>
    """
  end

  @doc """
  Renders a comment form with a textarea and submit button.

  ## Examples

      <.comment_form
        name="reply"
        value={@reply_text}
        placeholder="Add your reply..."
        button_text="Send"
        phx-change="update_reply"
        phx-submit="send_reply"
      />
  """
  attr :name, :string, required: true, doc: "the name of the textarea input"
  attr :value, :string, default: "", doc: "the current value of the textarea"
  attr :label, :string, default: "", doc: "the optional title at the top of the text area"

  attr :placeholder, :string,
    default: "Add your comment...",
    doc: "the placeholder text for the textarea"

  attr :button_text, :string, default: "Post", doc: "the text for the submit button"

  attr :button_class, :string,
    default: "bg-indigo-600 hover:bg-indigo-500",
    doc: "the class for the submit button"

  attr :rows, :integer, default: 3, doc: "the number of rows for the textarea"
  attr :disabled, :boolean, default: false, doc: "whether the form is disabled"
  attr :rest, :global, doc: "additional attributes to add to the form"

  def comment_form(assigns) do
    ~H"""
    <div class="flex items-start space-x-4">
      <div class="min-w-0 flex-1">
        <form action="#" class="relative" {@rest}>
          <label :if={@label != ""} class="text-gray-600 m-1">{@label}</label>
          <div class="rounded-lg bg-white outline outline-1 -outline-offset-1 outline-gray-300 focus-within:outline focus-within:outline-2 focus-within:-outline-offset-2 focus-within:outline-indigo-600">
            <label for={@name} class="sr-only">{@placeholder}</label>
            <textarea
              rows={@rows}
              name={@name}
              id={@name}
              value={@value}
              disabled={@disabled}
              class="block w-full resize-none bg-transparent px-3 py-1.5 text-base text-gray-900 placeholder:text-gray-400 focus:outline focus:outline-0 sm:text-sm/6 border-none"
              placeholder={@placeholder}
            ></textarea>

    <!-- Spacer element to match the height of the toolbar -->
            <div class="py-2" aria-hidden="true">
              <!-- Matches height of button in toolbar (1px border + 36px content height) -->
              <div class="py-px">
                <div class="h-9"></div>
              </div>
            </div>
          </div>

          <div class="absolute inset-x-0 bottom-0 flex justify-between py-2 pl-3 pr-2">
            <div class="flex items-center space-x-5">
              <!-- Placeholder for future utility buttons -->
            </div>
            <div class="shrink-0">
              <button
                type="submit"
                disabled={@disabled || String.trim(@value) == ""}
                class={[
                  "inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600",
                  @button_class
                ]}
              >
                {@button_text}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
    """
  end

  @doc """
  Renders a button.

  ## Examples

      <.button>Send!</.button>
      <.button phx-click="go" class="ml-2">Send!</.button>
  """
  attr :type, :string, default: nil
  attr :class, :string, default: nil
  attr :faux, :boolean, default: false, doc: "whether the button is faux (a div)"
  attr :rest, :global, include: ~w(disabled form name value)

  slot :inner_block, required: true

  def button(%{:faux => true} = assigns) do
    ~H"""
    <span
      type={@type}
      class={[
        "text-center phx-submit-loading:opacity-75 rounded-lg default:bg-zinc-700 default:hover:bg-zinc-500 py-1.5 px-2.5",
        "text-sm font-normal leading-6 default:text-white active:text-white/80",
        @class
      ]}
      {@rest}
    >
      {render_slot(@inner_block)}
    </span>
    """
  end

  def button(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 default:bg-blue-700 default:hover:bg-blue-500 rounded-lg py-1.5 px-2.5",
        "text-sm font-normal leading-6 default:text-white active:text-white/80",
        @class
      ]}
      {@rest}
    >
      {render_slot(@inner_block)}
    </button>
    """
  end

  attr :id, :string, required: true
  attr :content, :string, required: true
  attr :tiny, :boolean, default: false

  @doc """
  Copy to clipboard button.
  """
  def copy_button(assigns) do
    ~H"""
    <button
      id={@id}
      content={@content}
      phx-click={JS.dispatch("phx:copy", to: "##{@content}")}
      type="button"
      class={[
        "rounded-md inline-flex items-center bg-white dark:bg-transparent dark:hover:bg-transparent",
        if(not @tiny,
          do:
            "text-sm font-semibold text-gray-600 shadow-2xs ring-1 ring-inset ring-gray-300 hover:bg-gray-50 px-2.5 py-1.5",
          else: "text-xs text-gray-500 hover:text-gray-400"
        )
      ]}
    >
      Copy
    </button>
    """
  end

  attr :id, :string, required: true
  attr :type, :string, default: nil

  def id(assigns) do
    ~H"""
    <div class="text-xs text-gray-400 font-mono gap-0">
      <span class="pl-0 text-gray-500">
        <span :if={@type} class="text-right">{@type}:</span><span id="copy-id">{ @id }</span>
      </span>
      <.copy_button tiny id="copy-id-button" content="copy-id" />
    </div>
    """
  end

  @doc """
  A simple toggle wrapper with animations.
  """
  attr :id, :any, default: nil
  attr :checkbox_label, :string, default: "Toggle"
  attr :column, :boolean, default: false
  attr :class, :string, default: "w-1/2"
  attr :border, :string, default: ""
  attr :checkbox_field, Phoenix.HTML.FormField, doc: "the field to store the checkbox state"
  slot :inner_block, required: true
  slot :disabled_block

  def simple_toggle(assigns) do
    assigns =
      if assigns.border != "" do
        assigns
        |> assign(
          border_class: "m-1 p-2 border border-dashed border-1 rounded-md #{assigns.border}"
        )
      else
        assigns
        |> assign(border_class: "m-2")
      end

    assigns =
      if _form_truthy?(assigns.checkbox_field.value) do
        assigns
        |> assign(toggle_class: "delay-150 h-auto", disabled_class: "delay-0 p-0 opacity-0 h-0")
      else
        assigns
        |> assign(
          toggle_class: "delay-0 h-0 overflow-hidden",
          disabled_class: "delay-150 p-5 opacity-100 h-auto"
        )
      end

    ~H"""
    <div class={["flex flex-col", @border_class, @class]}>
      <.input field={@checkbox_field} type="checkbox" label={@checkbox_label} />
      <div class="static">
        <div class={[
          "transition-all ease-in-out duration-150 font-bold place-items-center",
          @disabled_class
        ]}>
          <div class="overflow-hidden">
            {render_slot(@disabled_block) || "Feature disabled"}
          </div>
        </div>
        <div class={["flex m-2 transition-all duration-150", @column && "flex-col", @toggle_class]}>
          {render_slot(@inner_block)}
        </div>
      </div>
    </div>
    """
  end

  def _form_truthy?(nil), do: false

  def _form_truthy?(value) do
    value in [true, true, "true", "on", "1"]
  end

  def _form_truthy?(_), do: false

  @doc """
  Renders an input with label and error messages.

  A `%Phoenix.HTML.Form{}` and field name may be passed to the input
  to build input names and error messages, or all the attributes and
  errors may be passed explicitly.

  ## Examples

      <.input field={@form[:email]} type="email" />
      <.input name="my-input" errors={["oh no!"]} />
  """
  attr :id, :any, default: nil
  attr :name, :any

  attr :label, :any,
    default: nil,
    doc: "Set to `nil` to not render a title field or the related HTML structure."

  attr :value, :any
  attr :target, :any
  attr :user, :any
  attr :hint, :string, default: "", doc: "small text to describe complicated fields"

  attr :type, :string,
    default: "text",
    values:
      ~w(checkbox color date datetime-local email file hidden month number password
               range radio search select tel text textarea time url user week threshold ratio dialer templated_textarea)

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"

  attr :no_label, :boolean, default: false
  attr :errors, :list, default: []
  attr :class, :string, default: nil
  attr :checked, :boolean, doc: "the checked flag for checkbox inputs"
  attr :wireless, :boolean, doc: "for dialer selection, indicates cannot load to landline"
  attr :prompt, :string, default: nil, doc: "the prompt for select inputs"
  attr :options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2"
  attr :users, :list, doc: "the users to pass to the userlist"
  attr :multiple, :boolean, default: false, doc: "the multiple flag for select inputs"
  attr :rest, :global, include: ~w(autocomplete cols disabled form max maxlength min minlength
                                   pattern phx-click placeholder readonly required rows size step)
  slot :inner_block

  def input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    errors = if Phoenix.Component.used_input?(field), do: field.errors, else: []

    assigns
    |> assign(field: nil, id: assigns.id || Phoenix.HTML.Form.input_name(field.form, field.name))
    |> assign(:errors, Enum.map(errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> input()
  end

  def input(%{type: "checkbox", value: value} = assigns) do
    assigns =
      assign_new(assigns, :checked, fn -> Phoenix.HTML.Form.normalize_value("checkbox", value) end)

    # Checkboxes are weird; We need a hidden input such that if the checkbox
    # is not checked, we still have a positive "false" to provide.
    # Under normal circumstances, HTML will not send the value for the false
    # checkbox, so we use a non-elixir/phoenix trick to include a default false,
    # and the 'value' for the checkbox is always true. It's simply that the
    # HTML form will not submit the true 'value' unless checked is true.
    ~H"""
    <div>
      <label class="flex items-center gap-3 my-1 text-sm leading-6 text-zinc-600">
        <input type="hidden" name={@name} value="false" />
        <input
          type="checkbox"
          id={@id || @name}
          name={@name}
          value="true"
          checked={@checked}
          class={[
            @class,
            "rounded-xs border-zinc-300 text-blue-600 text-opacity-95 focus:ring-blue-900"
          ]}
          {@rest}
        />
        {@label}
      </label>
      <.small>{@hint}</.small>
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  def input(%{type: "user"} = assigns) do
    transition_in =
      {"transition ease-in duration-100", "transform opacity-0 scale-95",
       "transform opacity-100 scale-100"}

    transition_out =
      {"transition ease-in duration-100", "transform opacity-100 scale-100",
       "transform opacity-0 scale-95"}

    assigns =
      assigns
      |> assign(transition_in: transition_in, transition_out: transition_out)

    ~H"""
    <div>
      <label id="listbox-label" for={@id} class="block text-sm font-medium leading-6 text-gray-900">
        {@label}
      </label>
      <div
        phx-click-away={JS.hide(to: "##{@id}-options-container", transition: @transition_out)}
        class="relative mt-2"
      >
        <button
          id={@id}
          phx-click={
            JS.toggle(to: "##{@id}-options-container", in: @transition_in, out: @transition_out)
          }
          type="button"
          class="mx-2 relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 sm:text-sm sm:leading-6"
          aria-haspopup="listbox"
          aria-expanded="true"
          aria-labelledby="listbox-label"
        >
          <span :if={!@user} class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              stroke="currentColor"
              class="h-5 w-5 shrink-0 rounded-full"
            >
              <circle cx="10" cy="6" r="4" stroke-width="2" />
              <path d="M3 18c1.5-3 4.5-3 7-3s5.5 0 7 3" stroke-width="2" />
            </svg>
            <span class="ml-3 block truncate">All Users</span>
          </span>

          <span :if={@user} class="flex items-center">
            <img src={@user.avatar_url} alt="" class="h-5 w-5 shrink-0 rounded-full" />
            <span class="font-normal ml-3 block truncate">{@user.name}</span>
          </span>

          <span class="pointer-events-none absolute inset-y-0 right-0 ml-3 flex items-center pr-2">
            <svg
              class="h-5 w-5 text-gray-400"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
        </button>

        <ul
          id={"#{@id}-options-container"}
          style="display: none;"
          class="absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden sm:text-sm"
          tabindex="-1"
          role="listbox"
          aria-labelledby="listbox-label"
          aria-activedescendant="listbox-option-3"
        >
          <!--
            Select option, manage highlight styles based on mouseenter/mouseleave and keyboard navigation.

            Highlighted: "bg-indigo-600 text-white", Not Highlighted: "text-gray-900"
          -->
          <li
            :for={user <- @users}
            :if={user}
            phx-click="set_user"
            phx-value-user_id={user.id}
            phx-target={@target}
            class="text-gray-900 relative cursor-default select-none py-2 pl-3 pr-9"
            id="listbox-option-0"
            role="option"
          >
            <div class="flex items-center">
              <img src={user.avatar_url} alt="" class="h-5 w-5 shrink-0 rounded-full" />
              <!-- Selected: "font-semibold", Not Selected: "font-normal" -->
              <span class={[@user && @user.id == user.id && "font-semibold", "ml-3 block truncate"]}>
                {user.name}
              </span>
            </div>
            <!--
              Checkmark, only display for selected option.

              Highlighted: "text-white", Not Highlighted: "text-indigo-600"
            -->
            <span
              :if={@user && @user.id == user.id}
              class="text-indigo-600 absolute inset-y-0 right-0 flex items-center pr-4"
            >
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path
                  fill-rule="evenodd"
                  d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
          </li>
          <!-- More items... -->
        </ul>
      </div>
    </div>
    """
  end

  def input(%{type: "select"} = assigns) do
    ~H"""
    <div>
      <.label for={@id}>{@label}</.label>
      <select
        id={@id}
        name={@name}
        class={[
          "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-2xs focus:outline-hidden focus:ring-zinc-500 focus:border-zinc-500 sm:text-sm",
          @class
        ]}
        multiple={@multiple}
        {@rest}
      >
        <option :if={@prompt} value="">{@prompt}</option>
        {Phoenix.HTML.Form.options_for_select(@options, @value)}
        <.small>{@hint}</.small>
      </select>
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  def input(%{type: "textarea"} = assigns) do
    ~H"""
    <div>
      <.label for={@id}>{@label}</.label>
      <textarea
        id={@id || @name}
        name={@name}
        class={[
          "mt-2 block min-h-[6rem] w-full rounded-lg border-zinc-300 py-[7px] px-[11px]",
          "text-zinc-900 focus:border-zinc-400 focus:outline-hidden focus:ring-4 focus:ring-zinc-800/5 sm:text-sm sm:leading-6",
          "phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400 phx-no-feedback:focus:ring-zinc-800/5",
          "border-zinc-300 focus:border-zinc-400 focus:ring-zinc-800/5",
          @errors != [] && "border-rose-400 focus:border-rose-400 focus:ring-rose-400/10"
        ]}
        {@rest}
      ><%= Phoenix.HTML.Form.normalize_value("textarea", @value) %></textarea>
      <.small>{@hint}</.small>
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  def input(%{type: "threshold"} = assigns) do
    ~H"""
    <div class="min-w-24">
      <div
        alt="Percent of contacts in segment"
        class={[
          "flex rounded-md shadow-2xs ring-1 ring-insetfocus-within:ring-2 focus-within:ring-inset",
          @errors != [] && "ring-rose-300 focus-within:ring-rose-600",
          @errors == [] && "ring-gray-300 focus-within:ring-indigo-600"
        ]}
      >
        <span
          :if={not @no_label}
          class={[
            "text-gray-500 flex select-none items-center pl-2 pr-3 sm:text-sm"
          ]}
        >
          {@label}
        </span>
        <input
          type="number"
          name={@name}
          id={@name}
          value={Phoenix.HTML.Form.normalize_value(@type, @value)}
          class="block w-12 border-0 bg-transparent px-1 py-0.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
          width="3rem"
          {@rest}
        />
        <span class="flex select-none items-center pr-2 text-gray-500 sm:text-sm">
          %
        </span>
      </div>
    </div>
    """
  end

  def input(%{type: "dialer"} = assigns) do
    ~H"""
    <div class="pl-2">
      <.label>
        {@label}
      </.label>
      <select
        id={@id}
        name={@name}
        class={[
          @class,
          "mt-2 block w-24 py-2 px-3 border border-gray-300 bg-white rounded-md shadow-2xs focus:outline-hidden focus:ring-zinc-500 focus:border-zinc-500 sm:text-sm"
        ]}
        multiple={@multiple}
        {@rest}
      >
        <option :if={@prompt} value="">{@prompt}</option>
        {Phoenix.HTML.Form.options_for_select(@options, @value)}
        <.small>{@hint}</.small>
      </select>
    </div>
    """
  end

  def input(%{type: "templated_textarea"} = assigns) do
    ~H"""
    <div class="pl-2">
      <.label>
        {@label}
      </.label>
      <div class="group flex items-center" aria-orientation="horizontal" role="tablist">
        <!-- Selected: "bg-gray-100 text-gray-900 hover:bg-gray-200", Not Selected: "bg-white text-gray-500 hover:bg-gray-100 hover:text-gray-900" -->
        <button
          id="tabs-1-tab-1"
          class="rounded-md border border-transparent bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900"
          aria-controls="tabs-1-panel-1"
          role="tab"
          type="button"
        >
          Write
        </button>
        <!-- Selected: "bg-gray-100 text-gray-900 hover:bg-gray-200", Not Selected: "bg-white text-gray-500 hover:bg-gray-100 hover:text-gray-900" -->
        <button
          id="tabs-1-tab-2"
          class="ml-2 disabled rounded-md border border-transparent bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900"
          aria-controls="tabs-1-panel-2"
          role="tab"
          type="button"
        >
          Preview (coming soon)
        </button>
        <!-- These buttons are here simply as examples and don't actually do anything. -->
        <div class="ml-auto hidden items-center space-x-5 group-has-[*:first-child[aria-selected='true']]:flex">
          <div class="flex items-center">
            <button
              type="button"
              class="-m-2.5 inline-flex size-10 items-center justify-center rounded-full text-gray-400 hover:text-gray-500"
            >
              <span class="sr-only">Insert link</span>
              <svg
                class="size-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
                data-slot="icon"
              >
                <path d="M12.232 4.232a2.5 2.5 0 0 1 3.536 3.536l-1.225 1.224a.75.75 0 0 0 1.061 1.06l1.224-1.224a4 4 0 0 0-5.656-5.656l-3 3a4 4 0 0 0 .225 5.865.75.75 0 0 0 .977-1.138 2.5 2.5 0 0 1-.142-3.667l3-3Z" />
                <path d="M11.603 7.963a.75.75 0 0 0-.977 1.138 2.5 2.5 0 0 1 .142 3.667l-3 3a2.5 2.5 0 0 1-3.536-3.536l1.225-1.224a.75.75 0 0 0-1.061-1.06l-1.224 1.224a4 4 0 1 0 5.656 5.656l3-3a4 4 0 0 0-.225-5.865Z" />
              </svg>
            </button>
          </div>
          <div class="flex items-center">
            <button
              type="button"
              class="-m-2.5 inline-flex size-10 items-center justify-center rounded-full text-gray-400 hover:text-gray-500"
            >
              <span class="sr-only">Insert code</span>
              <svg
                class="size-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
                data-slot="icon"
              >
                <path
                  fill-rule="evenodd"
                  d="M6.28 5.22a.75.75 0 0 1 0 1.06L2.56 10l3.72 3.72a.75.75 0 0 1-1.06 1.06L.97 10.53a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Zm7.44 0a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L17.44 10l-3.72-3.72a.75.75 0 0 1 0-1.06ZM11.377 2.011a.75.75 0 0 1 .612.867l-2.5 14.5a.75.75 0 0 1-1.478-.255l2.5-14.5a.75.75 0 0 1 .866-.612Z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
          <div class="flex items-center">
            <button
              type="button"
              class="-m-2.5 inline-flex size-10 items-center justify-center rounded-full text-gray-400 hover:text-gray-500"
            >
              <span class="sr-only">Mention someone</span>
              <svg
                class="size-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
                data-slot="icon"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.404 14.596A6.5 6.5 0 1 1 16.5 10a1.25 1.25 0 0 1-2.5 0 4 4 0 1 0-.571 2.06A2.75 2.75 0 0 0 18 10a8 8 0 1 0-2.343 5.657.75.75 0 0 0-1.06-1.06 6.5 6.5 0 0 1-9.193 0ZM10 7.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div class="mt-2">
        <div
          id="tabs-1-panel-1"
          class="-m-0.5 rounded-lg p-0.5"
          aria-labelledby="tabs-1-tab-1"
          role="tabpanel"
          tabindex="0"
        >
          <label for="comment" class="sr-only">Templated Content</label>
          <div>
            <textarea
              id={@id}
              name={@name}
              rows="5"
              class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              placeholder="Add your template..."
              {@rest}
            ><%= Phoenix.HTML.Form.normalize_value("textarea", @value) %></textarea>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def input(%{type: "ratio"} = assigns) do
    ~H"""
    <div>
      <.label :if={not @no_label}>{@label}</.label>
      <div
        alt="x contacts / 1 phone number"
        class="flex rounded-md shadow-2xs ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600"
      >
        <input
          type="number"
          name={@name}
          id={@name}
          value={Phoenix.HTML.Form.normalize_value(@type, @value)}
          class="block w-16 flex-1 border-0 bg-transparent py-1 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
          width="3rem"
          {@rest}
        />
        <span class="flex select-none items-center pr-3 text-gray-500 sm:text-sm">
          /1
        </span>
      </div>
    </div>
    """
  end

  def input(%{label: nil} = assigns) do
    ~H"""
    <div>
      <input
        type={@type}
        name={@name}
        id={@id || @name}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          @class,
          @errors != [] && "border-rose-400 focus:border-rose-400 focus:ring-rose-400/10"
        ]}
        {@rest}
      />
      <.small>{@hint}</.small>
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  def input(assigns) do
    ~H"""
    <div>
      <.label for={@id}>{@label}</.label>
      <input
        type={@type}
        name={@name}
        id={@id || @name}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          @class,
          "mt-2 block w-full rounded-lg border-zinc-300 py-[7px] px-[11px]",
          "text-zinc-900 focus:outline-hidden focus:ring-4 sm:text-sm sm:leading-6",
          "phx-no-feedback:border-zinc-300 phx-no-feedback:focus:border-zinc-400 phx-no-feedback:focus:ring-zinc-800/5",
          "border-zinc-300 focus:border-zinc-400 focus:ring-zinc-800/5",
          @errors != [] && "border-rose-400 focus:border-rose-400 focus:ring-rose-400/10"
        ]}
        {@rest}
      />
      <.small>{@hint}</.small>
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  @doc """
  Renders a container suitable to hold stats, see `stat/1`
  """
  attr :text, :string, default: nil
  slot :inner_block, required: true

  def stat_container(assigns) do
    ~H"""
    <div>
      <h3 :if={@text} class="text-base font-semibold text-gray-900">{@text}</h3>
      <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {render_slot(@inner_block)}
      </dl>
    </div>
    """
  end

  @doc """
  Renders a single stat. It contains a `text` header, `inner_block` as the primary stat, an
  optional `chart?`, optional `icon`, optional `trend_up`, `trend_down`, `trend_up?`, and an optional `href` + `href_text`
  """
  attr :text, :string, required: true
  attr :chart?, :boolean, default: false
  attr :trend, :string, default: nil
  attr :trend_up?, :boolean, default: nil
  attr :href, :any, default: nil
  attr :href_text, :string, default: nil
  attr :href_target, :string, default: "_self"
  slot :inner_block, required: true
  slot :icon, required: false

  def stat(assigns) do
    ~H"""
    <div class={[
      "relative rounded-lg bg-white shadow-xs",
      @chart? && "pt-5",
      not @chart? && "px-4 pt-5 pb-4 sm:px-6 sm:pt-6"
    ]}>
      <dt>
        <div
          :if={not is_nil(@icon)}
          class={["absolute rounded-md bg-indigo-500 p-3", @chart? && "left-4", not @chart? && ""]}
        >
          {render_slot(@icon)}
        </div>
        <p class={[
          "truncate text-sm font-medium text-gray-500",
          @chart? && "ml-20",
          not @chart? && "ml-16"
        ]}>
          {@text}
        </p>
      </dt>
      <dd class={[
        not @chart? && "ml-16",
        @chart? && "ml-0 pt-4 justify-center",
        "flex items-baseline pb-6 sm:pb-7"
      ]}>
        <p class="text-2xl font-semibold text-gray-900">
          {render_slot(@inner_block)}
        </p>
        <p
          :if={@trend && @trend_up?}
          class="ml-2 flex items-baseline text-sm font-semibold text-green-600"
        >
          <svg
            class="size-5 shrink-0 self-center text-green-500"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
            data-slot="icon"
          >
            <path
              fill-rule="evenodd"
              d="M10 17a.75.75 0 0 1-.75-.75V5.612L5.29 9.77a.75.75 0 0 1-1.08-1.04l5.25-5.5a.75.75 0 0 1 1.08 0l5.25 5.5a.75.75 0 1 1-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0 1 10 17Z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="sr-only"> Increased by </span>
          {@trend}
        </p>
        <p
          :if={@trend && not @trend_up?}
          class="ml-2 flex items-baseline text-sm font-semibold text-red-600"
        >
          <svg
            class="size-5 shrink-0 self-center text-red-500"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
            data-slot="icon"
          >
            <path
              fill-rule="evenodd"
              d="M10 3a.75.75 0 0 1 .75.75v10.638l3.96-4.158a.75.75 0 1 1 1.08 1.04l-5.25 5.5a.75.75 0 0 1-1.08 0l-5.25-5.5a.75.75 0 1 1 1.08-1.04l3.96 4.158V3.75A.75.75 0 0 1 10 3Z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="sr-only"> Decreased by </span>
          {@trend}
        </p>
        <div :if={@href} class="absolute inset-x-0 bottom-0 bg-gray-50 px-4 py-4 sm:px-6">
          <div class="text-sm">
            <a
              target={@href_target}
              href={@href}
              class="font-medium text-indigo-600 hover:text-indigo-500"
            >
              {@href_text}
            </a>
          </div>
        </div>
      </dd>
    </div>
    """
  end

  slot :inner_block, required: true

  def small(assigns) do
    ~H"""
    <small class="text-sm leading-5 text-zinc-500">{render_slot(@inner_block)}</small>
    """
  end

  @doc """
  Renders a label.

  ## Examples
      <.label for="email">Email</.label>
      <.label for={form[:email].name}>Email</.label>
  """
  attr :for, :string, default: nil
  attr :class, :string, default: nil
  slot :inner_block, required: true

  def label(assigns) do
    ~H"""
    <label for={@for} class={["block text-sm font-semibold leading-6 text-zinc-800", @class]}>
      {render_slot(@inner_block)}
    </label>
    """
  end

  @doc """
  Generates a generic error message.
  """
  slot :inner_block, required: true

  def error(assigns) do
    ~H"""
    <p class="phx-no-feedback:hidden mt-3 flex gap-3 text-sm leading-6 text-rose-600">
      <Heroicons.exclamation-circle-mini class="mt-0.5 w-5 h-5 flex-none" />
      {render_slot(@inner_block)}
    </p>
    """
  end

  @doc """
  Renders a header with title.
  """
  attr :class, :string, default: nil

  slot :inner_block, required: true
  slot :subtitle
  slot :actions

  def header(assigns) do
    ~H"""
    <header class={[@actions != [] && "flex items-center justify-between gap-6", @class]}>
      <div>
        <h1 class="text-lg font-semibold leading-8 text-zinc-800">
          {render_slot(@inner_block)}
        </h1>
        <p :if={@subtitle != []} class="mt-2 text-sm leading-6 text-zinc-600">
          {render_slot(@subtitle)}
        </p>
      </div>
      <div class="flex-none">{render_slot(@actions)}</div>
    </header>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <.table id="users" rows={@users}>
        <:col :let={user} label="id"><%= user.id %></:col>
        <:col :let={user} label="username"><%= user.username %></:col>
      </.table>
  """
  attr :id, :string, required: true
  attr :rows, :list, required: true
  attr :row_id, :any, default: nil, doc: "the function for generating the row id"
  attr :row_click, :any, default: nil, doc: "the function for handling phx-click on each row"
  attr :tight, :boolean
  attr :empty_text, :string, default: nil, doc: "the text to show when the table is empty"

  attr :row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"

  slot :col, required: true do
    attr :label, :string
  end

  slot :action, doc: "the slot for showing user actions in the last table column"

  def table(%{tight: _tight} = assigns) do
    assigns =
      with %{rows: %Phoenix.LiveView.LiveStream{}} <- assigns do
        assign(assigns, row_id: assigns.row_id || fn {id, _item} -> id end)
      end

    ~H"""
    <div class="overflow-y-auto px-4 sm:overflow-visible sm:px-0">
      <table class="mt-2 w-[40rem] sm:w-full">
        <thead class="text-left text-[0.8125rem] leading-6 text-zinc-500">
          <tr>
            <th :for={col <- @col} class="p-0 pb-4 pr-6 font-normal">{col[:label]}</th>
            <th class="relative p-0 pb-2"><span class="sr-only">{gettext("Actions")}</span></th>
          </tr>
        </thead>
        <tbody
          id={@id}
          phx-update={match?(%Phoenix.LiveView.LiveStream{}, @rows) && "stream"}
          class="relative divide-y divide-zinc-100 border-t border-zinc-200 text-sm leading-6 text-zinc-700"
        >
          <tr :for={row <- @rows} id={@row_id && @row_id.(row)} class="group hover:bg-zinc-50">
            <td
              :for={{col, i} <- Enum.with_index(@col)}
              phx-click={@row_click && @row_click.(row)}
              class={["relative p-0", @row_click && "hover:cursor-pointer"]}
            >
              <tr :if={match?([], @rows)} class="group hover:bg-zinc-50">
                <td colspan={@col |> Enum.count()} class="p-0 py-4 text-center text-zinc-500">
                  {@empty_text || gettext("No results found.")}
                </td>
              </tr>
              <div class="block py-1 pr-6">
                <span class="absolute -inset-y-px right-0 -left-4 group-hover:bg-zinc-50 sm:rounded-l-xl" />
                <span class={[col[:class], "relative", i == 0 && "font-semibold text-zinc-900"]}>
                  {render_slot(col, @row_item.(row))}
                </span>
              </div>
            </td>
            <td :if={@action != []} class="relative p-0 w-14">
              <div class="relative whitespace-nowrap py-4 text-right text-sm font-medium">
                <span class="absolute -inset-y-px -right-4 left-0 group-hover:bg-zinc-50 sm:rounded-r-xl" />
                <span
                  :for={action <- @action}
                  class="relative ml-4 font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
                >
                  {render_slot(action, @row_item.(row))}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    """
  end

  def table(assigns) do
    assigns =
      with %{rows: %Phoenix.LiveView.LiveStream{}} <- assigns do
        assign(assigns, row_id: assigns.row_id || fn {id, _item} -> id end)
      end

    ~H"""
    <div class="overflow-y-auto px-4 sm:overflow-visible sm:px-0">
      <table class="mt-11 w-[40rem] sm:w-full">
        <thead class="text-left text-[0.8125rem] leading-6 text-zinc-500">
          <tr>
            <th :for={col <- @col} class="p-0 pb-4 pr-6 font-normal">{col[:label]}</th>
            <th class="relative p-0 pb-4"><span class="sr-only">{gettext("Actions")}</span></th>
          </tr>
        </thead>
        <tbody
          id={@id}
          phx-update={match?(%Phoenix.LiveView.LiveStream{}, @rows) && "stream"}
          class="relative divide-y divide-zinc-100 border-t border-zinc-200 text-sm leading-6 text-zinc-700"
        >
          <tr :if={match?([], @rows)} class="group hover:bg-zinc-50">
            <td colspan={@col |> Enum.count()} class="p-0 py-4 text-center text-zinc-500">
              {@empty_text || gettext("No results found.")}
            </td>
          </tr>
          <tr :for={row <- @rows} id={@row_id && @row_id.(row)} class="group hover:bg-zinc-50">
            <td
              :for={{col, i} <- Enum.with_index(@col)}
              phx-click={@row_click && @row_click.(row)}
              class={["relative p-0", @row_click && "hover:cursor-pointer"]}
            >
              <div class="block py-4 pr-6">
                <span class="absolute -inset-y-px right-0 -left-4 group-hover:bg-zinc-50 sm:rounded-l-xl" />
                <span class={[col[:class], "relative", i == 0 && "font-semibold text-zinc-900"]}>
                  {render_slot(col, @row_item.(row))}
                </span>
              </div>
            </td>
            <td :if={@action != []} class="relative p-0 w-14">
              <div class="relative whitespace-nowrap py-4 text-right text-sm font-medium">
                <span class="absolute -inset-y-px -right-4 left-0 group-hover:bg-zinc-50 sm:rounded-r-xl" />
                <span
                  :for={action <- @action}
                  class="relative ml-4 font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
                >
                  {render_slot(action, @row_item.(row))}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    """
  end

  @doc """
  Renders a data list.

  ## Examples

      <.list>
        <:item title="Title"><%= @post.title %></:item>
        <:item title="Views"><%= @post.views %></:item>
      </.list>
  """
  slot :item, required: true do
    attr :title, :string, required: true
  end

  def list(assigns) do
    ~H"""
    <div class="mt-14">
      <dl class="-my-4 divide-y divide-zinc-100">
        <div :for={item <- @item} class="flex gap-4 py-4 sm:gap-8">
          <dt class="w-1/4 flex-none text-[0.8125rem] leading-6 text-zinc-500">{item.title}</dt>
          <dd class="text-sm leading-6 text-zinc-700">{render_slot(item)}</dd>
        </div>
      </dl>
    </div>
    """
  end

  @doc """
  Renders a back navigation link.

  ## Examples

      <.back navigate={~p"/posts"}>Back to posts</.back>
  """
  attr :navigate, :any, required: true
  slot :inner_block, required: true

  def back(assigns) do
    ~H"""
    <div class="mt-16">
      <.link
        navigate={@navigate}
        class="flex items-center gap-3 text-sm font-semibold leading-6 text-zinc-900 hover:text-zinc-700 dark:text-blue-400 dark:hover:text-blue-500"
      >
        <Heroicons.arrow_left solid class="w-3 h-3" />
        {render_slot(@inner_block)}
      </.link>
    </div>
    """
  end

  @doc """
  Renders a [Hero Icon](https://heroicons.com).

  Hero icons come in three styles – outline, solid, and mini.
  By default, the outline style is used, but solid an mini may
  be applied by using the `-solid` and `-mini` suffix.

  You can customize the size and colors of the icons by setting
  width, height, and background color classes.

  Icons are extracted from your `priv/hero_icons` directory and bundled
  within your compiled app.css by the plugin in your `assets/tailwind.config.js`.

  ## Examples

      <Heroicons.cake />
      <Heroicons.cake-solid />
      <Heroicons.cake-mini />
      <Heroicons.bolt class="bg-blue-500 w-10 h-10" />
  """
  attr :name, :string, required: true
  attr :class, :string, default: "h-5 h-5"

  attr :rest, :global

  @deprecated "Use `Heroicons.*/1 functions instead"
  def icon(%{name: "hero-" <> icon_name} = assigns) do
    assigns =
      assigns
      |> assign_decoded_heroicons_class(icon_name)

    ~H"""
    {@icon_block}
    """
  end

  def assign_decoded_heroicons_class(assigns, icon_name) do
    try do
      method =
        icon_name
        |> String.replace("-outline", "")
        |> String.replace("-solid", "")
        |> String.replace("-mini", "")
        |> String.replace("-micro", "")
        |> String.replace("-", "_")
        |> String.to_existing_atom()

      outline = icon_name |> String.match?(~r/-outline/)
      solid = icon_name |> String.match?(~r/-solid/)
      mini = icon_name |> String.match?(~r/-mini/)
      micro = icon_name |> String.match?(~r/-micro/)

      assigns =
        assigns
        |> assign(
          outline: outline || (not solid && not mini && not micro),
          solid: solid,
          mini: mini,
          micro: micro,
          rest: %{
            class: assigns[:class] || assigns.rest[:class]
          }
        )

      icon_block = apply(Heroicons, method, [assigns])

      assigns
      |> assign(:icon_block, icon_block)
    rescue
      ArgumentError ->
        assigns |> assign(:icon_block, false)
    end
  end

  @badge_theme %{
    "gray" => [
      "shadow-gray-500/50",
      "bg-gray-50 hover:bg-gray-100 dark:bg-gray-600/50 dark:hover:bg-gray-500/50",
      "text-gray-600 dark:text-brand-50",
      "ring-gray-500/80 hover:ring- dark:ring-gray-400/80"
    ],
    "red" => [
      "shadow-red-500/50",
      "bg-gray-50 hover:bg-red-100 dark:bg-gray-600/50 dark:hover:bg-red-500/50",
      "text-gray-600 hover:text-red-400 dark:text-brand-50 dark:hover:text-white",
      "ring-gray-500/10 hover:ring-red-400/20 dark:ring-red-500 dark:hover:ring-red-500/50"
    ],
    "yellow" => [
      "shadow-yellow-500/50",
      "bg-gray-50 hover:bg-blue-700/10 dark:bg-gray-600/50 dark:hover:bg-yellow-600/50",
      "text-gray-600 hover:text-blue-500 dark:text-brand-100 dark:hover:text-white",
      "ring-gray-500/10 hover:ring-blue-400/20 dark:ring-brand-200/10 dark:hover:ring-brand-400/20"
    ],
    "green" => [
      "shadow-green-500/50",
      "bg-green-50 dark:bg-green-500/10",
      "text-green-800 hover:text-green-500 dark:text-brand-100 dark:hover:text-white",
      "ring-green-600/20 hover:ring-green-400/20 dark:ring-brand-200/10 dark:hover:ring-brand-400/20"
    ],
    "blue" => [
      "shadow-blue-500/50",
      "bg-blue-50 hover:bg-blue-700/10 dark:bg-gray-600/50 dark:hover:bg-brand-600/50",
      "text-blue-600 hover:text-blue-500 dark:text-brand-100 dark:hover:text-white",
      "ring-blue-600/20 hover:ring-blue-400/20 dark:ring-brand-200/10 dark:hover:ring-brand-400/20"
    ],
    "indigo" => [
      "shadow-indigo-500/50",
      "bg-indigo-50 dark:bg-indigo-400/10",
      "text-indigo-600",
      "ring-indigo-600/10"
    ],
    "purple" => [
      "shadow-purple-500/50",
      "bg-purple-50 dark:bg-purple-400/10",
      "text-purple-600",
      "ring-purple-600/10"
    ],
    "pink" => [
      "shadow-pink-500/50",
      "bg-pink-50 dark:bg-pink-400/10"
    ],
    "cyan" => [
      "shadow-cyan-500/50",
      "bg-cyan-50",
      "text-cyan-800",
      "ring-cyan-600/20"
    ]
  }
  @text %{
    "gray" => "text-gray-600",
    "red" => "text-red-700",
    "yellow" => "text-yellow-800",
    "green" => "text-green-700",
    "blue" => "text-blue-700",
    "indigo" => "text-indigo-700",
    "purple" => "text-purple-700",
    "pink" => "text-pink-700",
    "dark" => %{
      "gray" => "text-gray-400",
      "red" => "text-red-400",
      "yellow" => "text-yellow-500",
      "green" => "text-green-400",
      "blue" => "text-blue-400",
      "indigo" => "text-indigo-400",
      "purple" => "text-purple-400",
      "pink" => "text-pink-400"
    }
  }
  @ring %{
    "gray" => "ring-gray-500/10",
    "red" => "ring-red-600/10",
    "yellow" => "ring-yellow-600/20",
    "green" => "ring-green-600/20",
    "blue" => "ring-blue-700/10",
    "indigo" => "ring-indigo-700/10",
    "purple" => "ring-purple-700/10",
    "pink" => "ring-pink-700/10",
    "dark" => %{
      "gray" => "ring-gray-400/20",
      "red" => "ring-red-400/20",
      "yellow" => "ring-yellow-400/20",
      "green" => "ring-green-500/20",
      "blue" => "ring-blue-400/30",
      "indigo" => "ring-indigo-400/30",
      "purple" => "ring-purple-400/30",
      "pink" => "ring-pink-400/20"
    }
  }

  @doc """
  Renders a badge.

  Badges come in eight colors – gray, red, yellow, green, blue, indigo, purple, and pink.
  By default, the gray color is used, but the other colors may be applied by using the :class attribute.

  Additionally, badges have a dark mode that can be enabled by passing the :dark=true attribute.

  Custom schemes can be used, set class to custom, and provide bg, ring, and text directly.

  ## Examples

      <.badge style="gray" />
      <.badge style="green" dark/>
      <.badge style="blue" />
      <.badge style="pink" style="bg-blue-500 w-10 h-10" />
      <.badg  style="custom"
              class="cursor-pointer"
              phx-click={
                JS.push("delete-item", value: %{id: item.id})
                |> hide("##lead_item-\#{@item.id}")
              }
              phx-value-some_id={@item.id}
              data-confirm="Really remove?"
              />
  """

  # TODO: Fix this ugly junk.
  ## CONSIDER:
  ##   <span
  ##   :if={@current.exception}
  ##   class="inline-flex items-center rounded-xs bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800"
  ## >
  ##   <svg class="mr-1.5 h-2 w-2 text-amber-400" fill="currentColor" viewBox="0 0 8 8">
  ##     <circle cx="4" cy="4" r="3" />
  ##   </svg>
  ##   Known Exception
  ## </span>
  @colors ~w(gray red yellow green blue indigo purple pink cyan custom)
  @style_colors ~w(gray red yellow green blue indigo purple pink cyan)

  attr :style, :string, values: @colors, default: "gray"
  attr :title, :string, default: nil
  attr :class, :string, default: nil
  attr :link?, :boolean, default: false
  attr :rest, :global
  slot :inner_block

  def badge(%{style: color} = assigns) when color in @style_colors do
    class =
      @badge_theme[color]
      |> Enum.join(" ")

    assigns
    |> assign(style: nil, class: class <> "#{assigns.class || ""}")
    |> badge()
  end

  def badge(assigns) do
    ~H"""
    <span
      class={[
        "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium shadow-md ring-1 ring-inset",
        @link? && "cursor-pointer font-heavy",
        @class
      ]}
      {@rest}
    >
      {render_slot(@inner_block)}
    </span>
    """
  end

  @status_colors %{
    "gray" => %{
      "ring-3" => "bg-gray-100",
      "text" => "text-gray-600",
      "dot" => "fill-gray-400"
    },
    "red" => %{
      "ring-3" => "bg-red-100",
      "text" => "text-red-700",
      "dot" => "fill-red-500"
    },
    "yellow" => %{
      "ring-3" => "bg-yellow-100",
      "text" => "text-yellow-800",
      "dot" => "fill-yellow-500"
    },
    "green" => %{
      "ring-3" => "bg-green-100",
      "text" => "text-green-700",
      "dot" => "fill-green-500"
    },
    "blue" => %{
      "ring-3" => "bg-blue-100",
      "text" => "text-blue-700",
      "dot" => "fill-blue-500"
    },
    "indigo" => %{
      "ring-3" => "bg-indigo-100",
      "text" => "text-indigo-700",
      "dot" => "fill-indigo-500"
    },
    "purple" => %{
      "ring-3" => "bg-purple-100",
      "text" => "text-purple-700",
      "dot" => "fill-purple-500"
    },
    "pink" => %{
      "ring-3" => "bg-pink-100",
      "text" => "text-pink-700",
      "dot" => "fill-pink-500"
    }
  }

  @doc """
  Renders a status dot of various colors.
  """
  attr :class, :string, values: @colors, default: "gray"
  attr :ring, :string
  attr :dot, :string
  attr :text, :string

  slot :inner_block

  def status_dot(%{ring: _, dot: _, text: _} = assigns) do
    # Tailwind UI Example is  dot = text-gray-500 ring =  bg-gray-100/10
    ~H"""
    <span class={[
      "inline-flex items-center gap-x-1.5 rounded-full px-1.5 py-0.5 text-xs font-medium",
      @ring,
      @text
    ]}>
      <svg class={["h-1.5 w-1.5", @dot]} viewBox="0 0 6 6" aria-hidden="true">
        <circle cx="3" cy="3" r="3" />
      </svg>
      {render_slot(@inner_block)}
    </span>
    """
  end

  def status_dot(%{class: color} = assigns) do
    %{
      "ring-3" => ring,
      "dot" => dot,
      "text" => text
    } = @status_colors[color]

    assigns
    # FIXME: colors wrong
    |> assign(class: nil, ring: ring, dot: dot, text: text)
    |> status_dot()
  end

  @doc """
  Renders a sub-tab, ment to be used under a <:tabset> slot.
  """
  attr :current?, :boolean, default: false
  attr :text, :string, required: true
  attr :alt, :string, default: ""

  attr :class, :string,
    default:
      "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-200 dark:hover:text-gray-100"

  attr :rest, :global, include: ~w(navigate patch href)

  def subtab(assigns) do
    ~H"""
    <.link
      {@rest}
      class={
        [
          "inline-flex items-center border-b-1 py-3 px-1 text-sm font-medium",
          @class,
          # WTF! It's like text-brand-400 doesn't exist...
          @current? &&
            "border-indigo-500 text-indigo-600 dark:text-brand-400 dark:hover:text-brand-300"
        ]
      }
      alt={@alt}
    >
      {@text}
    </.link>
    """
  end

  @doc """
  Renders a tab
  """
  slot :inner_block, required: true
  attr :current?, :boolean, default: false
  attr :alt, :string

  attr :class, :string, default: ""

  attr :sub_class, :string, default: ""
  attr :rest, :global, include: ~w(navigate patch href phx-click phx-value-tab-id)
  attr :subtabs, :list, default: []

  slot :icon, required: false

  def tab(assigns) do
    # Pat: Idk why I resorted to elixir reflection and direct execution...
    # It was easier than prop-drilling when I can't do sub slots
    assigns =
      try do
        icon = Map.get(assigns, :icon)
        method = String.to_existing_atom(icon)

        icon_assigns =
          assigns
          |> assign(:solid, assigns.current?)

        icon_block = apply(Heroicons, method, [icon_assigns])

        assigns
        |> assign(:icon_block, icon_block)
      rescue
        ArgumentError ->
          assigns |> assign(:icon_block, false)
      end

    ~H"""
    <.link
      {@rest}
      class={[
        "group inline-flex items-center border-b-2 py-2 px-1 text-sm font-medium",
        @class,
        if(@current?,
          do:
            "border-b-brand-500 hover:border-b-brand-600 dark:border-b-brand-300 text-brand-700 dark:text-brand-300",
          else:
            "border-transparent hover:border-b-gray-300 dark:hover:border-b-brand-300 text-gray-700 dark:text-gray-300"
        )
      ]}
      alt={@alt}
    >
      <span class={
        flatten_classes([
          "-ml-0.5 mr-2 h-5 w-5",
          @sub_class
        ])
      }>
        <%!-- This weird ahh hack because I want to keep the space for the icon --%>
        <%!-- Even if we haven't picked an icon for the tab... --%>
        {(@icon_block && @icon_block) || nil}
      </span>
      <span>{render_slot(@inner_block)}</span>
    </.link>
    """
  end

  @doc """
  Renders a group of tabs baked off of `live_action`.

  Requires multiple <:tab action={:example}>Title<:tab> slots.

  Optionally takes a dropdown? attr to render the tabset as a dropdown (for huge tabsets).
  """
  attr :id, :string, required: true
  attr :current_action, :atom, required: true

  attr :dropdown?, :boolean, default: false

  slot :tab, required: true do
    attr :action, :atom, required: true
    attr :alt, :string
    attr :icon, :string
    attr :active_icon, :string

    attr :navigate, :any
    attr :patch, :any
    attr :href, :any
    attr :phx_click, :any

    attr :subtabs, :list
    attr :sub_actions, :list
  end

  def tabset(%{dropdown?: true} = assigns) do
    ~H"""
    <div class="w-1/2">
      <div class="relative">
        <div class="flex flex-row gap-4 items-center">
          <span class="hidden sm:inline-block text-black-300">Show</span>
          <button
            type="button"
            id={"#{@id}-dropdown-button"}
            phx-click={JS.toggle(to: "##{@id}-dropdown-menu")}
            phx-click-away={JS.hide(to: "##{@id}-dropdown-menu")}
            class="relative w-full cursor-default rounded-md bg-white dark:bg-gray-800 py-2 pl-3 pr-10 text-left text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:text-sm sm:leading-6"
            aria-haspopup="listbox"
            aria-expanded="false"
          >
            <span class="flex items-center">
              <.icon
                :if={_get_current_tab(@tab, @current_action)[:icon]}
                name={_get_current_tab(@tab, @current_action)[:icon]}
                class="h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500"
              />
              <span class="ml-3 block truncate">
                {render_slot(_get_current_tab(@tab, @current_action), true)}
              </span>
            </span>
            <span class="pointer-events-none absolute inset-y-0 right-0 ml-3 flex items-center pr-2">
              <.icon name="hero-chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </span>
          </button>
        </div>
        <ul
          id={"#{@id}-dropdown-menu"}
          class="absolute z-10 mt-1 max-h-64 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-blue-200 dark:ring-brand-300/50 ring-opacity-5 focus:outline-none sm:text-sm hidden"
          role="listbox"
        >
          <li
            :for={tab <- @tab}
            class={[
              "relative cursor-default select-none py-2 pl-3 pr-9",
              if(_tab_active?(tab, @current_action),
                do: "bg-blue-600 text-white",
                else: "text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
              )
            ]}
            role="option"
            aria-selected={_tab_active?(tab, @current_action)}
            phx-click={_build_tab_click(@id, tab)}
            phx-value-tab-id={tab[:action]}
          >
            <div class="flex items-center">
              <.icon
                :if={tab[:icon]}
                name={tab[:icon]}
                class={[
                  "h-5 w-5 flex-shrink-0",
                  if(_tab_active?(tab, @current_action),
                    do: "text-white",
                    else: "text-gray-400 dark:text-gray-500"
                  )
                ]}
              />
              <span class={[
                "ml-3 block truncate",
                if(_tab_active?(tab, @current_action), do: "font-semibold", else: "font-normal")
              ]}>
                {render_slot(tab, _tab_active?(tab, @current_action))}
              </span>
            </div>

            <span
              :if={_tab_active?(tab, @current_action)}
              class="absolute inset-y-0 right-0 flex items-center pr-4 text-white"
            >
              <.icon name="hero-check" class="h-5 w-5" />
            </span>
          </li>
        </ul>
      </div>

    <!-- Subtabs for active tab (shown below dropdown) -->
      <div
        :for={tab <- @tab}
        :if={_tab_active?(tab, @current_action) and tab[:subtabs] not in [[], nil]}
        class="mt-2 border-t border-gray-200 dark:border-gray-600 pt-2"
      >
        <div class="flex flex-wrap gap-2">
          <.subtab
            :for={subtab <- tab[:subtabs]}
            current?={@current_action == subtab[:action]}
            text={subtab[:text]}
            navigate={subtab[:navigate]}
            patch={subtab[:patch]}
            href={subtab[:href]}
          />
        </div>
      </div>
    </div>
    """
  end

  def tabset(assigns) do
    ~H"""
    <div>
      <%!-- <div class="sm:hidden">
        <label for="tabs" class="sr-only">Select a tab</label>
        <!-- Use an "onChange" listener to redirect the user to the selected tab URL. -->
        <!-- TODO: Fix this, it's inop on small screens -->
        <select
          id="tabs"
          name="tabs"
          class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
        >
          <option>My Account</option>
          <option>Company</option>
          <option selected>Team Members</option>
          <option>Billing</option>
        </select>
      </div> --%>

      <div class="hidden sm:block">
        <div class="border-b border-gray-200 dark:border-gray-600">
          <nav area-label="Tab Navigation">
            <div
              class="-mb-px flex space-x-8 md:space-x-6 sm:space-x-4 overflow-x-auto"
              aria-label="Main Tabs"
            >
              <.tab
                :for={tab <- @tab}
                current?={_tab_active?(tab, @current_action)}
                alt={tab[:alt]}
                icon={tab[:icon]}
                navigate={tab[:navigate]}
                patch={tab[:patch]}
                href={tab[:href]}
                phx-click={tab[:phx_click]}
                phx-value-tab-id={tab[:action]}
                icon={tab[:icon]}
              >
                {render_slot(tab, _tab_active?(tab, @current_action))}
                <span
                  :if={tab[:subtabs] not in [[], nil] and not _tab_active?(tab, @current_action)}
                  class="bg-indigo-100 text-indigo-600 ring-1 ring-indigo-50 dark:ring-brand-200 dark:text-brand-50 dark:bg-brand-700/70 ml-3 hidden rounded-full py-0.5 px-2.5 text-xs font-medium md:inline-block"
                >
                  {length(tab[:subtabs])}
                </span>
              </.tab>
            </div>
            <div
              :for={tab <- @tab}
              :if={_tab_active?(tab, @current_action) and tab[:subtabs] not in [[], nil]}
              class="-mb-px flex space-x-8"
              aria-label="Sub Tabs"
            >
              <.subtab
                :for={subtab <- tab[:subtabs]}
                current?={@current_action == subtab[:action]}
                text={subtab[:text]}
                navigate={subtab[:navigate]}
                patch={subtab[:patch]}
                href={subtab[:href]}
              />
            </div>
          </nav>
        </div>
      </div>
    </div>
    """
  end

  attr :page, Ash.Page.Offset, required: true
  attr :on_turn_page, :string, required: true
  attr :current_page, :any, default: nil
  attr :middle_message, :string, default: nil

  def page_controls_offset(%{current_page: nil} = assigns) do
    # Handle the case where page might be nil or missing
    if !Map.has_key?(assigns, :page) || is_nil(assigns.page) do
      assigns =
        assigns
        |> assign(
          limit: 50,
          offset: 0,
          count: 0,
          more?: false,
          less_buttons: "disabled",
          more_buttons: "disabled",
          current_page: 1,
          pages: 1
        )

      page_controls_offset(assigns)
    else
      %{
        limit: limit,
        count: count,
        offset: offset,
        more?: more?
      } = assigns.page

      more_buttons = (more? && "cursor-pointer") || "disabled"
      less_buttons = (offset > 0 && "cursor-pointer") || "disabled"

      # Handle division by zero cases safely
      current_page =
        if limit <= 0 do
          1
        else
          try do
            ceil(offset / limit) + 1
          rescue
            ArithmeticError -> 1
          end
        end

      pages =
        if count <= 0 || limit <= 0 do
          1
        else
          try do
            ceil(count / limit)
          rescue
            ArithmeticError -> 1
          end
        end

      assigns =
        assigns
        |> assign(
          limit: limit,
          offset: offset,
          count: count,
          more?: more?,
          less_buttons: less_buttons,
          more_buttons: more_buttons,
          current_page: current_page,
          pages: pages
        )

      page_controls_offset(assigns)
    end
  end

  def page_controls_offset(assigns) do
    ~H"""
    <div class="flex items-center justify-between bg-white dark:bg-gray-600 px-2 py-1 sm:px-6 rounded-md">
      <div class="flex flex-1 justify-between sm:hidden">
        <a
          phx-click={@on_turn_page}
          phx-value-action={:back}
          class="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-500 px-4 py-1 text-sm font-medium text-gray-700 dark:text-brand-50 hover:bg-gray-50 dark:hover:bg-gray-500/50 drop-shadow-md"
        >
          Previous
        </a>
        <span :if={@middle_message} class="font-medium">
          {@middle_message}
        </span>
        <a
          phx-click={@on_turn_page}
          phx-value-action={:forward}
          class="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-500 px-4 py-1 text-sm font-medium text-gray-700 dark:text-brand-50 hover:bg-gray-50 dark:hover:bg-gray-500/50 drop-shadow-md"
        >
          Next
        </a>
      </div>
      <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700 dark:text-brand-50">
            <%= if @count > 0 do %>
              Showing <span class="font-medium">{@offset + 1}</span>
              to <span class="font-medium">{min(@offset + @limit, @count)}</span>
              of <span class="font-medium">{@count}</span>
              results
            <% else %>
              No results found
            <% end %>
          </p>
        </div>
        <span :if={@middle_message} class="font-medium">
          {@middle_message}
        </span>
        <div>
          <%= if @count > 0 do %>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-2xs" aria-label="Pagination">
              <button
                phx-click={@on_turn_page}
                phx-value-action={:first}
                disabled={@offset == 0}
                class={[
                  @less_buttons,
                  "relative inline-flex items-center rounded-l-md px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">First</span>
                <Heroicons.chevron_double_left class="w-5 h-5" />
              </button>
              <button
                phx-click={@on_turn_page}
                phx-value-action={:prev}
                disabled={@offset == 0}
                class={[
                  @less_buttons,
                  "relative inline-flex items-center px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">Previous</span>
                <Heroicons.chevron_left class="h-5 w-5" />
              </button>
              <span class="relative inline-flex items-center px-4 py-1 text-sm font-semibold text-gray-700 dark:text-brand-100 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">
                {"page #{@current_page} of #{@pages}"}
              </span>
              <button
                phx-click={@on_turn_page}
                phx-value-action={:next}
                disabled={not @more?}
                class={[
                  @more_buttons,
                  "relative inline-flex items-center px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">Next</span>
                <Heroicons.chevron_right class="h-5 w-5" />
              </button>
              <button
                phx-click={@on_turn_page}
                phx-value-action={:last}
                disabled={not @more?}
                class={[
                  @more_buttons,
                  "relative inline-flex items-center rounded-r-md px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">Last</span>
                <Heroicons.chevron_double_right class="h-5 w-5" />
              </button>
            </nav>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  attr :meta, :any, required: true
  attr :link_fn, :fun
  attr :on_turn_page, :string, required: true
  attr :current_page, :any, default: nil
  attr :middle_message, :string, default: nil

  def page_controls_flop(%{current_page: nil} = assigns) do
    meta = assigns.meta

    more_buttons = (meta.has_next_page? && "cursor-pointer") || "disabled"
    less_buttons = (meta.has_previous_page? && "cursor-pointer") || "disabled"

    assigns =
      assigns
      |> assign(
        limit: meta.flop.page_size,
        page: meta.current_page,
        count: meta.total_count,
        more?: meta.has_next_page?,
        less_buttons: less_buttons,
        more_buttons: more_buttons,
        current_page: meta.current_page,
        pages: meta.total_pages
      )

    page_controls_flop(assigns)
  end

  def page_controls_flop(assigns) do
    ~H"""
    <div class="flex items-center justify-between bg-white dark:bg-gray-600 px-2 py-1 sm:px-6 rounded-md">
      <div class="flex flex-1 justify-between sm:hidden">
        <.link
          patch={@link_fn.(FlopHelpers.flop_query_dir(@meta, :prev))}
          rel="prev"
          class="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-500 px-4 py-1 text-sm font-medium text-gray-700 dark:text-brand-50 hover:bg-gray-50 dark:hover:bg-gray-500/50 drop-shadow-md"
        >
          Previous
        </.link>
        <span :if={@middle_message} class="font-medium">
          {@middle_message}
        </span>
        <.link
          patch={@link_fn.(FlopHelpers.flop_query_dir(@meta, :next))}
          rel="next"
          class="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-500 px-4 py-1 text-sm font-medium text-gray-700 dark:text-brand-50 hover:bg-gray-50 dark:hover:bg-gray-500/50 drop-shadow-md"
        >
          Next
        </.link>
      </div>
      <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700 dark:text-brand-50">
            <%= if @count > 0 do %>
              Showing <span class="font-medium">{max(1, @page * @limit - @limit)}</span>
              to <span class="font-medium">{min(@page * @limit, @count)}</span>
              of <span class="font-medium">{@count}</span>
              results
            <% else %>
              No results found
            <% end %>
          </p>
        </div>
        <span :if={@middle_message} class="font-medium">
          {@middle_message}
        </span>
        <div>
          <%= if @count > 0 do %>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-2xs" aria-label="Pagination">
              <.link
                patch={@link_fn.(FlopHelpers.flop_query_dir(@meta, :first))}
                rel="first"
                disabled={@page == 1}
                class={[
                  @less_buttons,
                  "relative inline-flex items-center rounded-l-md px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">First</span>
                <Heroicons.chevron_double_left class="w-5 h-5" />
              </.link>
              <.link
                patch={@link_fn.(FlopHelpers.flop_query_dir(@meta, :prev))}
                rel="prev"
                disabled={@page == 1}
                class={[
                  @less_buttons,
                  "relative inline-flex items-center px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">Previous</span>
                <Heroicons.chevron_left class="h-5 w-5" />
              </.link>
              <span class="relative inline-flex items-center px-4 py-1 text-sm font-semibold text-gray-700 dark:text-brand-100 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">
                {"page #{@current_page} of #{@pages}"}
              </span>
              <.link
                patch={@link_fn.(FlopHelpers.flop_query_dir(@meta, :next))}
                rel="next"
                disabled={not @more?}
                class={[
                  @more_buttons,
                  "relative inline-flex items-center px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">Next</span>
                <Heroicons.chevron_right class="h-5 w-5" />
              </.link>
              <.link
                patch={@link_fn.(FlopHelpers.flop_query_dir(@meta, :last))}
                rel="last"
                disabled={not @more?}
                class={[
                  @more_buttons,
                  "relative inline-flex items-center rounded-r-md px-2 py-1 text-gray-400 dark:text-brand-50 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500/50 focus:z-20 focus:outline-offset-0"
                ]}
              >
                <span class="sr-only">Last</span>
                <Heroicons.chevron_double_right class="h-5 w-5" />
              </.link>
            </nav>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  @doc """
    This component renders a search bar and results pop-up.

  """
  attr :items, :any, required: true
  attr :event, :any, required: true
  attr :value, :any
  attr :target, :any
  attr :click_away, JS, default: %JS{}
  attr :patch_prefix, :string, required: true

  def search_popup(assigns) do
    ~H"""
    <button id="bar" class="flex w-full xs:w-auto" phx-click={show_modal("pop-up")}>
      <div class="grow relative border border-gray-100 bg-white dark:border-gray-700 dark:bg-gray-600 rounded-xl">
        <svg
          class="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-400 dark:text-brand-200"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fill-rule="evenodd"
            d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
            clip-rule="evenodd"
          />
        </svg>
        <input
          type="text"
          class="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-gray-900 dark:text-brand-100 placeholder:text-gray-400 dark:placeholder:text-brand-200 focus:ring-0 sm:text-sm"
          placeholder="Search..."
          role="combobox"
          aria-expanded="false"
          aria-controls="options"
        />
      </div>
    </button>

    <div id="pop-up" class="relative z-10 hidden" role="dialog" aria-modal="true">
      <div
        id="pop-up-bg"
        class="fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity"
        aria-hidden="true"
      >
      </div>
      <div
        id="pop-up-container"
        class="fixed inset-0 z-10 w-screen overflow-y-auto p-4 sm:p-6 md:p-20"
      >
        <div
          phx-click-away={hide_modal(@click_away, "pop-up")}
          class="mx-auto max-w-xl transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all"
        >
          <div class="">
            <form for={@value} id="search_form" phx-change={@event} phx-debounce="25" target={@target}>
              <svg
                class="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                  clip-rule="evenodd"
                />
              </svg>
              <input
                id="pop-up-content"
                name={:search_value}
                autofocus
                value={@value}
                id="to_search"
                type="text"
                class="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                placeholder="Search by Setup Name or GAD Ticket ID..."
                role="combobox"
                aria-expanded="false"
                aria-controls="options"
              />
            </form>
          </div>
          <%!-- <!-- Results, show/hide based on command palette state --> --%>
          <ul
            :if={!is_nil(@items) and not Enum.empty?(@items)}
            class="max-h-96 transform-gpu scroll-py-3 overflow-y-auto p-3"
            id="options"
            role="listbox"
          >
            <%!-- Active: "bg-gray-100" --%>

            <li
              :for={item <- @items}
              phx-click={hide_modal(JS.patch(@patch_prefix <> "/#{item.id}/"), "pop-up")}
              class="group flex cursor-default select-none rounded-xl p-3 cursor-pointer"
              id="option-1"
              role="option"
              tabindex="-1"
            >
              <%!-- <div class="flex h-10 w-10 flex-none items-center justify-center rounded-lg bg-indigo-500">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                </svg>
              </div> --%>
              <div class="ml-4 flex-auto">
                <%!-- <!-- Active: "text-gray-900", Not Active: "text-gray-700" -->
                <p class="text-sm font-medium text-gray-700"><%= item.name %> </p>--%>
                <div class="flex items-start gap-x-3 pb-1">
                  <p class="text-sm font-medium text-gray-700">{item.name}</p>
                  <%!-- <.setup_state state={item.state} /> --%>
                </div>
                <p :if={Map.has_key?(item, :ticket)} class="text-sm font-light text-gray-500">
                  {item.ticket}
                </p>
                <p :if={not Map.has_key?(item, :ticket)} class="text-sm font-light text-gray-500">
                  <.datetime
                    id={"item-" <> item.id <> "-inserted-at"}
                    short?
                    dt={item.inserted_at |> DateTime.to_naive()}
                  />
                </p>
              </div>
            </li>

            <%!-- <!-- More items... --> --%>
          </ul>

          <%!-- <!-- Empty state, show/hide based on command palette state --> --%>
          <div
            :if={is_nil(@items) or Enum.empty?(@items)}
            class="px-6 py-14 text-center text-sm sm:px-14"
          >
            <svg
              class="mx-auto h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
              />
            </svg>
            <p class="mt-4 font-semibold text-gray-900">No results found</p>
            <p class="mt-2 text-gray-500">No items found for this search term. Please try again.</p>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp _tab_active?(tab, current_action) do
    tab[:action] == current_action or
      (is_list(tab[:sub_actions]) and current_action in tab[:sub_actions])
  end

  defp _get_current_tab(tabs, current_action) do
    Enum.find(tabs, fn tab -> _tab_active?(tab, current_action) end) || List.first(tabs)
  end

  defp _build_tab_click(id, tab) do
    cond do
      tab[:navigate] -> JS.navigate(tab[:navigate]) |> JS.toggle(to: "##{id}-dropdown-menu")
      tab[:patch] -> JS.patch(tab[:patch]) |> JS.toggle(to: "##{id}-dropdown-menu")
      tab[:href] -> JS.navigate(tab[:href]) |> JS.toggle(to: "##{id}-dropdown-menu")
      tab[:phx_click] -> tab[:phx_click]
      true -> nil
    end
  end

  @doc """
  Renders a well.

  """
  attr :class, :string, default: ""
  slot :inner_block, required: true

  def well(assigns) do
    ~H"""
    <div class={[@class, "rounded-lg px-4 py-5 sm:p-6 bg-gray-50 overflow-scroll w-1/2 md:w-full"]}>
      {render_slot(@inner_block)}
    </div>
    """
  end

  @doc """
  This component displays a well with a mono gray font.

  This is best used for simple, generic code that does not require
  syntax highlighting.
  """
  slot :inner_block, required: true

  def code(assigns) do
    ~H"""
    <.well>
      <pre class="text-xs font-mono text-gray-900">
        <%= render_slot(@inner_block) %>
      </pre>
    </.well>
    """
  end

  ## JS Commands

  def show(js \\ %JS{}, selector) do
    JS.show(js,
      to: selector,
      transition:
        {"transition-all transform ease-out duration-300",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
         "opacity-100 translate-y-0 sm:scale-100"}
    )
  end

  def hide(js \\ %JS{}, selector) do
    JS.hide(js,
      to: selector,
      time: 200,
      transition:
        {"transition-all transform ease-in duration-200",
         "opacity-100 translate-y-0 sm:scale-100",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"}
    )
  end

  def show_modal(js \\ %JS{}, id) when is_binary(id) do
    js
    |> JS.show(to: "##{id}")
    |> JS.show(
      to: "##{id}-bg",
      transition: {"transition-all transform ease-out duration-300", "opacity-0", "opacity-100"}
    )
    |> show("##{id}-container")
    |> JS.add_class("overflow-hidden", to: "body")
    |> JS.focus_first(to: "##{id}-content")
  end

  def hide_modal(js \\ %JS{}, id) do
    js
    |> JS.hide(
      to: "##{id}-bg",
      transition: {"transition-all transform ease-in duration-200", "opacity-100", "opacity-0"}
    )
    |> hide("##{id}-container")
    |> JS.hide(to: "##{id}", transition: {"block", "block", "hidden"})
    |> JS.remove_class("overflow-hidden", to: "body")
    |> JS.pop_focus()
  end

  @doc """
  Generates tag for inlined form input errors.
  """
  def error_tag(assigns) do
    ~H"""
    <.error :for={msg <- @errors}>{msg}</.error>
    """
  end

  @doc """
  Translates an error message using gettext.
  """
  def translate_error({msg, opts}) do
    # When using gettext, we typically pass the strings we want
    # to translate as a static argument:
    #
    #     # Translate "is invalid" in the "errors" domain
    #     dgettext("errors", "is invalid")
    #
    #     # Translate the number of files with plural rules
    #     dngettext("errors", "1 file", "%{count} files", count)
    #
    # Because the error messages we show in our forms and APIs
    # are defined inside Ecto, we need to translate them dynamically.
    # This requires us to call the Gettext module passing our gettext
    # backend as first argument.
    #
    # Note we use the "errors" domain, which means translations
    # should be written to the errors.po file. The :count option is
    # set by Ecto and indicates we should also apply plural rules.
    if count = opts[:count] do
      Gettext.dngettext(AdminWeb.Gettext, "errors", msg, msg, count, opts)
    else
      Gettext.dgettext(AdminWeb.Gettext, "errors", msg, opts)
    end
  end

  @doc """
  Translates the errors for a field from a keyword list of errors.
  """
  def translate_errors(errors, field) when is_list(errors) do
    for {^field, {msg, opts}} <- errors, do: translate_error({msg, opts})
  end
end
