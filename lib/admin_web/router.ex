defmodule AdminWeb.Router do
  # MARK: Macros/Imports
  use AdminWeb, :router
  import AshAdmin.Router

  import AdminWeb.Policies.Helpers

  import AdminWeb.UserAuth,
    only: [fetch_current_user: 2]

  import AdminWeb.LiveInit, only: [init_assigns: 2]

  import Oban.Web.Router
  import Phoenix.LiveDashboard.Router
  import PhoenixStorybook.Router

  # MARK: Pipelines
  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, {AdminWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
    plug :init_assigns
  end

  pipeline :setup_policy do
    plug Bodyguard.Plug.Authorize,
      policy: AdminWeb.Policies.Setups,
      action: &action_live_view/1,
      user: &user/1,
      fallback: AdminWeb.FallbackController

    plug :init_assigns
  end

  pipeline :crm_policy do
    plug Bodyguard.Plug.Authorize,
      policy: AdminWeb.Policies.CampaignManagement,
      action: &action_live_view/1,
      user: &user/1,
      fallback: AdminWeb.FallbackController

    plug :init_assigns
  end

  pipeline :super_admins_only do
    plug Bodyguard.Plug.Authorize,
      policy: AdminWeb.Policies.SuperAdmin,
      action: &action_live_view/1,
      user: &user_with_roles/1,
      fallback: AdminWeb.FallbackController

    plug :init_assigns
  end

  pipeline :api do
    plug :accepts, ["json"]
  end

  pipeline :webhooks do
    plug :accepts, ["json"]
  end

  pipeline :sso_layout do
    plug :put_root_layout, {AdminWeb.Layouts, :sso}
  end

  pipeline :authenticated do
    plug AdminWeb.UserAuth, :require_authenticated_user
  end

  pipeline :admin_only do
    plug AdminWeb.UserAuth, :require_admin_user
  end

  # MARK: System Scopes
  scope "/" do
    storybook_assets()
  end

  scope "/", AdminWeb do
    pipe_through [:browser]
    live_storybook("/storybook", backend_module: AdminWeb.Storybook)

    # Development examples
    live_session :examples,
      on_mount: [{AdminWeb.LiveInit, :current_user}] do
      live "/examples/pie-charts", Examples.PieChartLive, :index
    end
  end

  scope "/" do
    pipe_through [:browser, :authenticated, :admin_only]

    ash_admin("/mega-admin")
  end

  scope "/" do
    pipe_through [:browser, :authenticated]

    live_dashboard "/dashboard",
      metrics: AdminWeb.Telemetry
  end

  # MARK: End System
  scope "/", AdminWeb do
    # Use the default browser stack
    pipe_through :browser

    get "/health", HealthCheckController, :index
  end

  # MARK: Webhooks
  scope "/wh/", AdminWeb do
    pipe_through [:webhooks]
    get "/:key/calleridapp/added", WebhookController, :cid_added
  end

  # MARK: OAuth
  scope "/", AdminWeb do
    pipe_through [:browser]

    get "/sign_out", OAuthCallbackController, :sign_out, as: :sign_out
    get "/oauth/callbacks/:provider", OAuthCallbackController, :new
    get "/verifyToken", OldSSOController, :verify_token

    live_session :default,
      on_mount: [{AdminWeb.LiveInit, :current_user}] do
      live "/", HomeLive.Index, :index
      live "/sign_in", SignInLive.Index, :index, as: :sign_in
    end
  end

  scope "/sso", AdminWeb do
    pipe_through [:browser, :sso_layout]

    get "/", OldSSOController, :index

    get "/login", OldSSOController, :login_page
    post "/login", OldSSOController, :login

    get "/logout", OldSSOController, :logout

    get "/not_authorized", OldSSOController, :not_authorized

    get "/verifyToken", OldSSOController, :verify_token
  end

  # MARK: Time Tracking
  scope "/time_tracking/", AdminWeb do
    pipe_through [:browser]

    live_session :time_tracking,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/tasks", TaskLive.Index, :index
      live "/tasks/new", TaskLive.Index, :new
      live "/tasks/:id/edit", TaskLive.Index, :edit

      live "/tasks/:id", TaskLive.Show, :show
      live "/tasks/:id/show/edit", TaskLive.Show, :edit

      live "/punches", PunchLive.Index, :index
      live "/punches/start", PunchLive.Index, :start
      live "/punches/stop", PunchLive.Index, :stop
      live "/punches/new", PunchLive.Index, :new
      live "/punches/:id/edit", PunchLive.Index, :edit

      live "/punches/:id", PunchLive.Show, :show
      live "/punches/:id/show/edit", PunchLive.Show, :edit

      live "/punch", PunchLive.Punch, :index
      live "/punch/start", PunchLive.Punch, :start
    end
  end

  # MARK: Org Mgmt
  scope "/org", AdminWeb do
    pipe_through [:browser]

    live_session :org,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/business_entities", BusinessEntityLive.Index, :index
      live "/business_entities/new", BusinessEntityLive.Index, :new
      live "/business_entities/:id/edit", BusinessEntityLive.Index, :edit

      live "/business_entities/:id", BusinessEntityLive.Show, :show
      live "/business_entities/:id/show/edit", BusinessEntityLive.Show, :edit

      live "/centers", CenterLive.Index, :index
      live "/centers/new", CenterLive.Index, :new
      live "/centers/:id/edit", CenterLive.Index, :edit

      live "/centers/:id", CenterLive.Show, :show
      live "/centers/:id/show/edit", CenterLive.Show, :edit

      live "/teams", TeamLive.Index, :index
      live "/teams/new", TeamLive.Index, :new
      live "/teams/:id/edit", TeamLive.Index, :edit

      live "/teams/:id", TeamLive.Show, :show
      live "/teams/:id/show/edit", TeamLive.Show, :edit

      live "/users", UserLive.Index, :index
    end
  end

  # MARK: Transcription
  scope "/transcription", AdminWeb do
    pipe_through [:browser]

    live_session :mailers,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/admin", Transcription.AdminLive.Index, :index
      live "/debug", Transcription.MailerLive.Index, :index

      live "/mailers", Transcription.MailerLive.Index, :index
      live "/mailers/new", Transcription.MailerLive.Index, :new
      live "/mailers/:id/edit", Transcription.MailerLive.Index, :edit

      live "/mailers/:id", Transcription.MailerLive.Show, :show
      live "/mailers/:id/show/edit", Transcription.MailerLive.Show, :edit

      live "/forms", Transcription.FormLive.Index, :index
      live "/forms/all", Transcription.FormLive.Index, :all
      live "/forms/new", Transcription.FormLive.Index, :new
      live "/forms/:id/edit", Transcription.FormLive.Edit, :index

      live "/forms/:id/edit/:field_id", Transcription.FormLive.Edit, :edit_field
      live "/forms/:id/edit/:field_id/move", Transcription.FormLive.Edit, :move_field

      live "/forms/:id/edit/:field_id/confirm_delete",
           Transcription.FormLive.Edit,
           :confirm_delete

      live "/forms/:id", Transcription.FormLive.Show, :show

      live "/review/", Transcription.ReviewLive.Panel, :search
      live "/review/search_results", Transcription.ReviewLive.Panel, :results
    end

    live_session :mailers_transcription,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/transcribe", Transcription.TranscribeLive.Index, :index
      live "/transcribe/:id", Transcription.TranscribeLive.Index, :lookup
      live "/transcribe/:id/fields", Transcription.TranscribeLive.Index, :lookup_fields
    end
  end

  # MARK: SMS
  scope "/messaging", AdminWeb do
    pipe_through [:browser, :authenticated]

    live_session :sms,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/qc", SmsQcLive.Index, :index
      live "/inbound_sms", InboundSmsLive.Index, :index
      live "/inbound_sms/new", InboundSmsLive.Index, :new
      live "/inbound_sms/:id/edit", InboundSmsLive.Index, :edit

      live "/inbound_sms/:id", InboundSmsLive.Show, :show
      live "/inbound_sms/:id/show/edit", InboundSmsLive.Show, :edit

      live "/outbound_text_messages", OutboundSmsLive.Index, :index
      live "/outbound_text_messages/new", OutboundSmsLive.Index, :new
      live "/outbound_text_messages/:id/edit", OutboundSmsLive.Index, :edit

      live "/outbound_text_messages/:id", OutboundSmsLive.Show, :show
      live "/outbound_text_messages/:id/show/edit", OutboundSmsLive.Show, :edit

      # Development tools - only available in dev/test environments
      if Application.get_env(:admin, :environment) in [:dev, :test] do
        live "/intercepted_sms", InterceptedSmsLive.Index, :index
        live "/intercepted_sms/new_mock", InterceptedSmsLive.Index, :new_mock
      end

      live "/outbound_text_batches", OutboundSmsBatchLive.Index, :index
      live "/outbound_text_batches/new", OutboundSmsBatchLive.Index, :new
      live "/outbound_text_batches/stage/:stage_filter", OutboundSmsBatchLive.Index, :index
      live "/outbound_text_batches/:id/edit", OutboundSmsBatchLive.Index, :edit
      live "/outbound_text_batches/:id/audit", SmsAuditSelectionLive.Select, :select

      live "/outbound_text_batches/:id", OutboundSmsBatchLive.Index, :show
      live "/outbound_text_batches/:id/show/edit", OutboundSmsBatchLive.Show, :edit

      live "/threads", SmsThreadLive.Index, :index

      live "/thread/:id", SmsThreadLive.Show, :show
      live "/thread/:id/show/edit", SmsThreadLive.Show, :edit
    end
  end

  # MARK: Jobs / Oban
  scope "/jobs", AdminWeb do
    pipe_through [:browser, :authenticated]

    oban_dashboard("/oban")

    live_session :jobs,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/", JobLive.Index, :index
      live "/all", JobLive.Index, :all
      live "/new", JobLive.Index, :new

      live "/backfiller", JobLive.Status, :backfiller
      live "/shopify_loader", JobLive.Status, :shopify_loader
      live "/shopify_refund_loader", JobLive.Status, :shopify_refund_loader
      live "/shopify_refund_walker", JobLive.Status, :shopify_refund_walker

      live "/gorgias_ticket_loader", JobLive.Status, :gorgias_ticket_loader
      live "/gorgias_ticket_message_loader", JobLive.Status, :gorgias_ticket_message_loader
      live "/gorgias_ticket_event_loader", JobLive.Status, :gorgias_ticket_event_loader
      live "/gorgias_user_loader", JobLive.Status, :gorgias_user_loader
      live "/gorgias_csat_loader", JobLive.Status, :gorgias_csat_loader

      live "/dummy_job", JobLive.Status, :dummy_job

      live "/:id/edit", JobLive.Index, :edit

      live "/:id", JobLive.Show, :show
      live "/:id/show/edit", JobLive.Show, :edit
    end
  end

  # MARK: Reports
  scope "/reports", AdminWeb do
    pipe_through [:browser, :authenticated]

    live_session :reports,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      live "/adr", ReportsLive.AdrReports, :adr_reports
      # live "/inbound_sms", InboundSmsLive.Index, :index
      # live "/inbound_sms/new", InboundSmsLive.Index, :new
      # live "/inbound_sms/:id/edit", InboundSmsLive.Index, :edit
    end
  end

  # MARK: CRM
  # scope "/", AdminWeb do
  #   # Redirections from old versions of the app.
  #   forward "/setups", Plugs.SetupsRedirector
  # end

  scope "/crm", AdminWeb do
    pipe_through [:browser, :authenticated]

    # MARK: |Lead Files
    scope "/lead_files" do
      pipe_through [:crm_policy]

      live_session :lead_files,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/", LeadFileLive.Index, :index
        live "/stage/:stage_filter", LeadFileLive.Index, :index
        live "/:id", LeadFileLive.Index, :show
      end

      get "/download/:id", LeadFileController, :download
      get "/download_scrub/:id", LeadFileController, :download_scrub
    end

    # MARK: |DNCs
    scope "/dncs" do
      pipe_through [:crm_policy]

      live_session :dncs,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/", DncLive.Index, :index
      end

      get "/download/:project_id", DncController, :download
    end

    # MARK: |Setups

    scope "/setups" do
      pipe_through [:setup_policy]

      live_session :setups,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/new_legacy", SetupLive.NewLegacy, :new
        live "/load_legacy", LoadLive.Index, :index

        # V2 lead loader
        live "/", SetupLive.Index, :index
        live "/stage/:stage_filter", SetupLive.Index, :index
        live "/new", SetupLive.Index, :new
        live "/:id/edit", SetupLive.Index, :edits
        # live "/:id", SetupLive.Show, :show
        live "/:id", SetupLive.Index, :show
        live "/:id/upload", SetupLive.Upload, :edit
        live "/:id/plan", SetupLive.Plan, :edit
        live "/:id/process", SetupLive.Process, :process
        live "/:id/process/:tab", SetupLive.Process, :process
        # live "/:id/load", SetupLive.Process, :load
        live "/:id/review", SetupLive.Review, :review

        live "/:id/archive", SetupLive.Show, :archive
        live "/:id/discard", SetupLive.Show, :discard
      end
    end

    # MARK: |Campaign Tools
    scope "/campaign_tools" do
      # /campaign_tools
      pipe_through [:crm_policy]

      live_session :campaigns,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/group_leads_reset", CampaignMgmtLive.GroupLeadsReset, :group_leads_reset
        live "/contactability", CampaignMgmtLive.Contactability, :contactability
        # TODO: Add lead-search
        # TODO: Add DNC tool
      end
    end

    # MARK: |Efforts
    scope "/efforts" do
      pipe_through [:crm_policy]

      live_session :efforts,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/", Crm.EffortsLive.Index, :index
        live "/:id", Crm.EffortsLive.Index, :show
        # TODO: Add effort index
      end
    end

    # MARK: |Files
    scope "/files" do
      pipe_through [:crm_policy]

      live_session :files,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/", FileLive.Index, :index

        live "/upload", FileLive.Upload, :index

        # Acting with a file.
        live "/:id", FileLive.Show, :show
        live "/:id/edit", FileLive.Show, :edit
        live "/:id/stage", FileLive.Stage, :edit
        live "/:id/pre-analyze", FileLive.Analyze, :edit
        live "/:id/analyze", FileLive.Analyze, :analyze
        live "/:id/verify", FileLive.Verify, :edit
        live "/:id/send", FileLive.Send, :edit
        live "/:id/send_preview/:other_id", FileLive.Send, :preview

        # Main page of each stage; Look for additional files available at each stage.
        live "/stage", FileLive.Stage, :index
        live "/analyze", FileLive.Analyze, :index
        live "/verify", FileLive.Verify, :index
        live "/send", FileLive.Send, :index
      end
    end

    # MARK: |Baked Data
    scope "/baked_data" do
      pipe_through [:crm_policy]

      live_session :baked_data,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/", BakedDataLive.Index, :index
        live "/rebake", BakedDataLive.Index, :show
      end
    end
  end

  # MARK: Bundle Scope
  scope "/bundle", AdminWeb do
    pipe_through [:browser, :authenticated, :super_admins_only]
    get "/export/:effort_id", BundleController, :export

    live_session :bundle_export,
      on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
      pipe_through [:crm_policy]
      live "/export", BundleLive.Export, :index
    end
  end

  if Application.get_env(:admin, :allow_dev_routes?) == true do
    scope "/dev", AdminWeb do
      pipe_through [:browser, :fetch_current_user]

      forward "/mailbox", Plug.Swoosh.MailboxPreview

      get "/user/:role", Controllers.DevController, :user
      post "/user/id", Controllers.DevController, :user_by_id
    end
  end

  # MARK: Dev Scope
  if Application.compile_env(:admin, :environment) == :dev do
    scope "/bundle", AdminWeb do
      pipe_through [:browser, :authenticated]

      live_session :bundle_import,
        on_mount: [{AdminWeb.LiveInit, :ensure_authenticated}] do
        live "/import", BundleLive.Index, :index
        live "/import/upload", BundleLive.Index, :upload
      end
    end
  end
end
