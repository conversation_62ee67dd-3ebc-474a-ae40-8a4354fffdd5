defmodule AdminWeb.SetupLive.Helpers do
  import Ecto.Query, warn: false
  alias Admin.AdminRepo
  alias Admin.Crm.LeadFile
  alias Admin.Crm.Setups.Setup
  alias Oban.Job

  alias Admin.Crm.LeadFile.{OperationRecord, LoadRecord}

  def build_states(lead_files) do
    lead_files
    |> Enum.map(fn lead_file ->
      {lead_file.id, lead_file.state || :pending}
    end)
    |> Enum.into(%{})
  end

  def build_tasks(lead_files, tasks) do
    lead_files
    |> Enum.map(fn lead_file ->
      current_task =
        tasks
        |> Enum.find(fn {lf, _, _} -> lf.id == lead_file.id end)
        |> elem(1)

      {lead_file.id, current_task |> _parse_type()}
    end)
    |> Enum.into(%{})
  end

  def job_prefix, do: Application.get_env(:admin, Oban)[:prefix]

  defp _parse_type({:load, _}), do: :load
  defp _parse_type({:operation, %OperationRecord{type: :append}}), do: :append
  defp _parse_type({:operation, %OperationRecord{type: :suppress}}), do: :suppress

  @doc """
  Lookup lead file
  """
  def job_for(%LeadFile{last_job_id: id}) when not is_nil(id), do: get_oban_job(id)
  def job_for(%LeadFile{id: id}), do: find_oban_job("lead_file", id)

  def job_for(_),
    do: {:error, "must provide a lead_file to get single latest job, for more see jobs_for/1"}

  @doc """
  Get a list of all jobs.
  For LeadFiles, it is every Job which exists for the lead file, including old jobs if they still exist.
  For Setups, it is every Job that was spawned for this setup, across all `LeadFile`s.
  """
  def jobs_for(%Setup{id: id, job_ids: []}), do: find_oban_jobs("setup", id)
  def jobs_for(%Setup{job_ids: job_ids}) when is_list(job_ids), do: get_oban_jobs(job_ids)
  def jobs_for(%Setup{id: id}), do: find_oban_jobs("setup", id)
  def jobs_for(%LeadFile{id: id}), do: find_oban_jobs("lead_file", id)
  def jobs_for(_), do: {:error, "must provide LeadFile or Setups.Setup to get all jobs"}

  @doc """
  Find a singilar Oban.Job by id
  """
  def get_oban_job(job_id) do
    from(j in Job, where: j.id == ^job_id)
    |> AdminRepo.one(prefix: job_prefix())
  end

  @doc """
  Find multiple Oban.Jobs by ids
  """
  def get_oban_jobs(job_ids) when is_list(job_ids) do
    from(j in Job, where: j.id in ^job_ids)
    |> AdminRepo.Replica.all(prefix: job_prefix())
  end

  @doc """
  Searches for jobs by key (setup or lead_file) and value (the id of the param)
  """
  def find_oban_jobs(arg_key, arg_value) do
    from(j in Job, where: j.args[^arg_key] == ^arg_key)
    |> AdminRepo.Replica.all(prefix: job_prefix())
  end

  @doc """
  This finds the latest job by arg key, for all jobs, see find_oban_jobs/2
  """
  def find_oban_job(arg_key, arg_value) do
    from(
      j in Job,
      where: j.args[^arg_key] == ^arg_key,
      order_by: [desc: j.id],
      limit: 1
    )
    |> AdminRepo.one(prefix: job_prefix())
  end
end
