defmodule AdminWeb.JobLive.Status do
  use AdminWeb, :live_view

  require Logger

  use AdminWeb.Live.TelemetryBinder,
    binding_name: "job_status"

  import Ecto.Query, warn: false

  alias Admin.Jobs
  alias Admin.Jobs.Job

  # These are all of the Gorgias Loaders
  # Admin.Integrations.Gorgias.Loaders.{CSATLoader, EventLoader, TicketLoader, TicketMessageLoader, UserLoader}
  @jobs %{
    backfiller: "Crm.Baking.Backfiller",
    shopify_loader: "Admin.Integrations.Shopify.LoaderWorker",
    shopify_refund_loader: "Admin.Integrations.Shopify.RefundLoaderWorker",
    shopify_refund_walker: "Admin.Integrations.Shopify.Jobs.RefundWalker",
    gorgias_csat_loader: "Admin.Integrations.Gorgias.Loaders.CSATLoaderWorker",
    gorgias_ticket_event_loader: "Admin.Integrations.Gorgias.Loaders.EventLoaderWorker",
    gorgias_ticket_loader: "Admin.Integrations.Gorgias.Loaders.TicketLoaderWorker",
    gorgias_ticket_message_loader: "Admin.Integrations.Gorgias.Loaders.TicketMessageLoaderWorker",
    gorgias_user_loader: "Admin.Integrations.Gorgias.Loaders.UserLoaderWorker",
    dummy_job: "Admin.Jobs.DummyRecursiveJob"
  }

  @type gorgias_loader ::
          :gorgias_csat_loader
          | :gorgias_event_loader
          | :gorgias_ticket_loader
          | :gorgias_ticket_message_loader
          | :gorgias_user_loader
  @type shopify_loader :: :shopify_loader | :shopify_refund_loader | :shopify_refund_walker
  @type worker :: :backfiller | shopify_loader() | gorgias_loader() | :dummy_job
  @spec worker_name(worker :: worker) :: String.t()
  def worker_name(worker) do
    @jobs
    |> Map.get(worker)
  end

  @start [:oban, :job, :start]
  @stop [:oban, :job, :stop]
  @exception [:oban, :job, :exception]

  @events [
    @start,
    @stop,
    @exception
  ]

  @known_loaders ~w(
      shopify_loader
      shopify_refund_loader
      shopify_refund_walker
      gorgias_ticket_loader
      gorgias_ticket_message_loader
      gorgias_ticket_event_loader
      gorgias_user_loader
      gorgias_csat_loader
      dummy_job
    )a

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(connected?: false)
     |> assign(dev?: Admin.Application.env() == :dev)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :backfiller, _params) do
    socket
    |> assign(:page_title, "Backfiller Status")
    |> assign(:job, find_job(:backfiller, :active))
    |> stream(:jobs, find_job(:backfiller) || [], reset: true)
  end

  defp apply_action(socket, live_action, _params) when live_action in @known_loaders do
    socket
    |> assign(:page_title, "#{live_action |> Atom.to_string() |> Macro.camelize()} Loader Status")
    |> assign(:job, find_job(live_action, :active))
    |> stream(:jobs, find_job(live_action) || [], reset: true)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Job")
    |> assign(:job, %Job{})
  end

  defp apply_action(socket, :index, _params) do
    attach_many(socket, @events, retry?: true)

    socket
    |> assign(:page_title, "Viewing All Jobs")
    |> assign(:job, nil)
  end

  # Lookup functions
  def find_job(worker_name, :active) do
    _find_job(:active, @jobs[worker_name])
  end

  def find_job(worker_name, :scheduled) do
    _find_job(:scheduled, @jobs[worker_name])
  end

  def _find_job(:active, worker) do
    from(j in Job,
      prefix: ^_prefix(),
      select: j,
      where: j.worker == ^worker and j.state in ~w(available executing),
      order_by: [desc: j.inserted_at],
      # don't error/raise if there's more than one
      limit: 1
    )
    |> Admin.AdminRepo.one()
  end

  def _find_job(:scheduled, worker) do
    from(j in Job,
      prefix: ^_prefix(),
      select: j,
      where: j.worker == ^worker and j.state in ~w(scheduled),
      order_by: [desc: j.inserted_at],
      # first 10 + 1 to see if there's more
      limit: 11
    )
    |> Admin.AdminRepo.Replica.all()
  end

  def find_job(worker_name) do
    _find_job(@jobs[worker_name])
  end

  def _find_job(worker) do
    from(j in Job,
      prefix: ^_prefix(),
      select: j,
      where: j.worker == ^worker,
      order_by: [desc: j.inserted_at],
      # first 10 + 1 to see if there's more
      limit: 11
    )
    |> Admin.AdminRepo.Replica.all()
  end

  @impl true
  def handle_info({AdminWeb.JobLive.FormComponent, {:saved, job}}, socket) do
    {:noreply, stream_insert(socket, :jobs, job)}
  end

  # Oban Job Handling

  def handle_info({[:oban, :job, action], _measures, %{job: job}}, socket)
      when action in ~w(start exception)a do
    {:noreply, stream_insert(socket, :jobs, job, at: 0)}
  end

  def handle_info({[:oban, :job, :stop], _measures, %{state: state, job: job}}, socket) do
    job =
      %Oban.Job{
        job
        | state: Atom.to_string(state)
      }

    {:noreply, stream_insert(socket, :jobs, job, at: 0)}
  end

  def handle_info(something_else, socket) do
    Logger.info("🗑️ [Job LiveView - Status] Unhandled Event: #{inspect(something_else)}")
    {:noreply, socket |> put_flash(:warning, "Unhandled Event, See logs.")}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    job = Jobs.get_job!(id)
    {:ok, _} = Jobs.delete_job(job)

    {:noreply, stream_delete(socket, :jobs, job)}
  end

  def to_badge_class("available"), do: "gray"
  def to_badge_class("scheduled"), do: "blue"
  def to_badge_class("executing"), do: "green"
  def to_badge_class("success"), do: "indigo"
  def to_badge_class("completed"), do: "indigo"
  def to_badge_class("discarded"), do: "red"
  def to_badge_class("retryable"), do: "yellow"
  def to_badge_class(_), do: "gray"

  defp _prefix, do: Application.fetch_env!(:admin, Oban)[:prefix]
end
