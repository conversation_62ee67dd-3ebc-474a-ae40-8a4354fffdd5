defmodule AdminWeb.SmsAuditSelectionLive.Select do
  use AdminWeb, :live_component

  alias Admin.Messaging.{OutboundSMSBatch, SMSThread, BatchAudit}

  require Ash.Query
  require Ecto.Query
  import Ecto.Query

  # MARK: Schemas
  defmodule Option do
    use Ecto.Schema
    import Ecto.Changeset

    embedded_schema do
      field :decision, :string
      field :sample_size, :float
      field :count, :integer
    end

    def changeset(option, params \\ %{}) do
      option
      |> cast(params, [:decision, :sample_size, :count])
      |> validate_number(:sample_size,
        greater_than_or_equal_to: 0,
        less_than_or_equal_to: 100,
        message: "must be between 0% and 100%"
      )
      |> validate_required([:decision, :sample_size])
      |> Map.put(:action, :insert)
    end
  end

  defmodule Options do
    use Ecto.Schema
    import Ecto.Changeset

    embedded_schema do
      embeds_many :options, Option
    end

    def changeset(options, params \\ %{}) do
      options
      |> cast(params, [])
      |> cast_embed(:options)
      |> Map.put(:action, :insert)
    end
  end

  # MARK: Lifecycle
  @impl Phoenix.LiveComponent
  def mount(socket) do
    cs = options_changeset()

    socket =
      socket
      |> assign(
        batch: nil,
        audit_record: nil,
        decisions_found: [],
        breakdown: [],
        selection_options: %{},
        selection_results: %{},
        changeset: cs,
        form: to_form(cs, as: "options")
      )

    {:ok, socket}
  end

  @impl Phoenix.LiveComponent
  def update(%{id: _id, batch: batch} = assigns, socket) do
    # Get the audit record for this batch
    audit_record = get_audit_record(batch)

    socket =
      socket
      |> assign(assigns)
      |> assign(:page_title, "Select Outbound SMS Batch")
      |> assign(:batch, batch)
      |> assign(:audit_record, audit_record)
      |> preview_selection(batch)
      |> default_selection_options()

    # If we have an audit record and selection results, clear them to show the existing audit
    socket =
      if audit_record && socket.assigns[:selection_results] &&
           socket.assigns.selection_results != %{} do
        assign(socket, :selection_results, %{})
      else
        socket
      end

    {:ok, socket}
  end

  @impl Phoenix.LiveComponent
  def handle_event("select", %{"options" => params}, socket) do
    {:noreply, perform_selection(socket, params)}
  end

  @impl Phoenix.LiveComponent
  def handle_event("validate_selections", %{"options" => params}, socket) do
    cs = options_changeset(params)
    form = to_form(cs, as: "options")

    socket =
      socket
      |> assign(
        selection_options: parse_selection_options(params),
        changeset: cs,
        form: form
      )

    {:noreply, socket}
  end

  @impl Phoenix.LiveComponent
  def handle_event("execute", _params, socket) do
    # Get the selection results from the socket
    selection_results = socket.assigns.selection_results

    # Format options for the BatchAudit
    options =
      socket.assigns.selection_options
      |> Enum.map(fn %{decision: type, sample_size: percentage} ->
        # Use string keys to be consistent with what's stored in the database
        %{"type" => type, "percentage" => percentage}
      end)

    # Create the BatchAudit record
    params = %{
      "selected_by_id" => socket.assigns.current_user.id,
      "batch_id" => socket.assigns.batch.id,
      "options" => options,
      "selection_ended_at" => DateTime.utc_now()
    }

    audit_record =
      BatchAudit
      |> Ash.Changeset.for_create(:create, params)
      |> Ash.create!()

    # Associate selected threads with the BatchAudit
    selected_thread_ids =
      selection_results
      |> Enum.flat_map(fn {_type, threads} ->
        Enum.map(threads, & &1.id)
      end)

    # Update the selected threads to associate them with the BatchAudit
    from(t in SMSThread,
      where: t.id in ^selected_thread_ids
    )
    |> Admin.AdminRepo.update_all(
      [set: [batch_audit_id: audit_record.id]],
      prefix: "messaging"
    )

    # Calculate thread counts by decision type for the success message
    thread_counts_by_decision =
      selection_results
      |> Enum.map(fn {decision, threads} ->
        "#{length(threads)} #{decision}"
      end)
      |> Enum.join(", ")

    socket =
      socket
      |> assign(:audit_record, audit_record)
      |> put_flash(
        :info,
        "Batch audit created successfully with #{length(selected_thread_ids)} threads selected (#{thread_counts_by_decision})."
      )

    # Send an update to the parent component to refresh batch audits and close the modal
    send_update(
      AdminWeb.OutboundSmsBatchLive.Components.Show,
      %{
        id: socket.assigns.batch.id,
        refresh_batch_audits: true,
        audit_id: audit_record.id
      }
    )

    {:noreply, socket}
  end

  # MARK: Helper functions
  defp options_changeset(params \\ %{}) do
    %Options{}
    |> Options.changeset(params)
  end

  defp preview_selection(socket, batch) do
    # TODO: Also exclude statically mapped threads.

    replied_thread_breakdown =
      from(t in SMSThread,
        where:
          t.batch_id == ^batch.id and not is_nil(t.decision) and t.decision != "HumanRequired",
        group_by: t.decision,
        select: %{decision: t.decision, count: count(t.id)}
      )
      |> Admin.AdminRepo.Replica.all(prefix: "messaging")

    decisions_found =
      replied_thread_breakdown
      |> Enum.map(fn %{decision: type} -> type end)

    socket
    |> assign(:breakdown, replied_thread_breakdown)
    |> assign(:decisions_found, decisions_found)
  end

  defp perform_selection(socket, params) do
    # Perform selection
    # TODO: Also exclude statically mapped threads.
    results =
      socket.assigns.selection_options
      |> Enum.map(fn %{decision: type, sample_size: percentage, count: count} ->
        # Calculate the number of threads to select based on percentage
        selected_count = ceil(count * (percentage / 100))
        {type, select_by_decision(socket.assigns.batch.id, type, selected_count)}
      end)
      |> Enum.sort_by(fn {_, rec} -> length(rec) end, :desc)

    socket
    |> assign(:selection_results, results)
  end

  defp default_selection_options(socket) do
    breakdown =
      socket.assigns.breakdown
      |> Enum.map(fn
        %{decision: "SA"} = dec -> Map.put(dec, :sample_size, 5)
        dec -> Map.put(dec, :sample_size, 2.5)
      end)

    cs = options_changeset(%{options: breakdown})

    socket
    |> assign(
      selection_options: breakdown,
      changeset: cs,
      form: to_form(cs, as: "options")
    )
  end

  defp parse_selection_options(params) do
    params
    |> Map.get("options")
    |> Enum.map(fn {_id, opt} ->
      {sample_size, _} = opt["sample_size"] |> Float.parse()

      %{
        decision: opt["decision"],
        sample_size: sample_size,
        count: opt["count"] |> String.to_integer()
      }
    end)
  end

  defp decision_count(breakdown, decision) do
    breakdown
    |> Enum.find(&(&1.decision == decision))
    |> Map.get(:count)
  end

  defp select_by_decision(batch_id, decision, count) do
    # TODO: Also exclude statically mapped threads.
    from(t in SMSThread,
      where: t.batch_id == ^batch_id and t.decision == ^decision,
      order_by: fragment("md5(batch_id::text || id::text)"),
      limit: ^count,
      select: t
    )
    |> Admin.AdminRepo.Replica.all(prefix: "messaging")
    |> Ash.load!([:outbound_messages, :inbound_messages])
  end

  defp add_selection_option(socket, label, percentage) do
    selection_options =
      socket.assigns.selection_options
      |> Map.put(label, percentage)

    socket
    |> assign(:selection_options, selection_options)
  end

  defp get_option(selection_options, type) do
    item =
      selection_options
      |> Enum.find(&(&1.decision == type))

    case item do
      %{sample_size: percent, count: count} ->
        "#{percent}% (#{ceil(count * (percent / 100))})"

      _ ->
        ""
    end
  end

  defp get_audit_record(batch) do
    batch.id
    |> BatchAudit.for_batch()
    |> case do
      {:ok, audit_record} ->
        # Try to load the selected_by relationship, but handle errors gracefully
        try do
          # Use a read action that includes the selected_by relationship
          BatchAudit
          |> Ash.get!(audit_record.id, load: [:selected_by])
        rescue
          # If loading fails for any reason, just return the original record
          _ -> audit_record
        end

      {:error, _} ->
        nil
    end
  end
end
