defmodule AdminWeb.OutboundSmsBatchLive.Components.Show do
  @moduledoc """
  This module is responsible for handling the show component of the Outbound SMS Batch LiveView.

  It is used to display the details of a specific SMS batch, including its statistics, audits, and approvals.
  """
  use AdminWeb, :live_component

  require Logger

  import AdminWeb.GadStyles.Components.Spinner
  import AdminWeb.OutboundSmsBatchLive.Components

  alias Phoenix.LiveView.AsyncResult

  alias Admin.Messaging.{
    OutboundSMSBatch,
    OutboundSMS,
    InboundSMS,
    SMSThread,
    OutboundSMSBatchApproval,
    BatchAudit
  }

  alias Admin.Crm.LeadFile.StagedContact

  require Ash.Query

  require Ecto.Query
  import Ecto.Query
  alias Admin.AdminRepo

  require Ecto.Query
  import Ecto.Query
  alias Admin.AdminRepo

  # MARK: Lifecycle
  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign(:lead_count, AsyncResult.loading())
      |> assign(:delivered, AsyncResult.loading())
      |> assign(:bounced, AsyncResult.loading())
      |> assign(:message_count, AsyncResult.loading())
      # Used for decision breakdown
      |> assign(:response_rate, AsyncResult.loading())
      # For storing batch audits
      |> assign(:batch_audits, [])
      |> assign(:add, "false")
      # Default active tab
      |> assign(:active_tab, "stats")
      |> assign(
        delivered_count: 0,
        delivered_records: 0,
        delivered_ratio_pct: 0
      )

    {:ok, socket}
  end

  @impl true
  # Handle refresh_batch_audits update from the audit selection component
  def update(%{id: id, refresh_batch_audits: true, audit_id: audit_id}, socket) do
    # Refresh the batch data
    batch = Ash.get!(OutboundSMSBatch, id)

    # Refresh batch audits
    batch_audits = get_batch_audits(batch.id)

    socket =
      socket
      |> assign(:batch, batch)
      |> assign(:batch_audits, batch_audits)
      |> put_flash(:info, "Batch audit created successfully with ID: #{audit_id}")
      |> push_event("hide_modal", %{id: "sms-audit-modal"})

    # Restart the async tasks to refresh the data
    socket =
      socket
      |> start_async(:decision_breakdown, fn -> get_stat(batch, :decision_breakdown) end)
      |> start_async(:message_count, fn -> get_stat(batch, :message_count) end)

    {:ok, socket}
  end

  @impl true
  def update(%{id: id, current_user: current_user}, socket) do
    # Check if we're switching to a different batch
    current_batch_id = if socket.assigns[:batch], do: socket.assigns.batch.id, else: nil
    switching_batch = current_batch_id && current_batch_id != id

    with {:ok, batch} <- OutboundSMSBatch |> Ash.get(id) do
      # Fetch batch audits
      batch_audits = get_batch_audits(batch.id)

      {:ok, approvers} =
        OutboundSMSBatchApproval |> Ash.Query.filter(batch_id == ^id) |> Ash.read()

      approval_states =
        Enum.map(approvers, fn approver ->
          approver.state
        end)

      Logger.debug("Approvers: #{inspect(approvers)}")

      socket =
        socket
        |> assign(batch: batch)
        |> assign(current_user: current_user)
        |> assign(batch_audits: batch_audits)
        |> assign(batch_approvers: approvers)
        |> assign(
          all_approved?:
            if(Enum.all?(approval_states, fn state -> state == :approved end),
              do: true,
              else: false
            )
        )
        |> assign(current_approver_responses: [])
        |> assign(add: "true")

      # Reset all async results when switching batches to ensure clean state
      socket =
        if switching_batch do
          socket
          |> assign(:lead_count, AsyncResult.loading())
          |> assign(:delivered, AsyncResult.loading())
          |> assign(:bounced, AsyncResult.loading())
          |> assign(:message_count, AsyncResult.loading())
          |> assign(:response_rate, AsyncResult.loading())
          |> assign(
            delivered_count: 0,
            delivered_records: 0,
            delivered_ratio_pct: 0
          )
        else
          socket
        end

      # Start async tasks to load data
      socket =
        socket
        |> start_async(:lead_count, fn -> get_stat(batch, :lead_count) end)
        |> start_async(:bounced, fn -> get_stat(batch, :bounced) end)
        |> start_async(:message_count, fn -> get_stat(batch, :message_count) end)
        |> start_async(:decision_breakdown, fn -> get_stat(batch, :decision_breakdown) end)

      {:ok, socket}
    else
      {:error, _message} ->
        socket =
          socket
          |> push_navigate(to: ~p"/messaging/outbound_text_batches/")
          |> put_flash(:warning, "Batch not found.")

        {:ok, socket}
    end
  end

  # MARK: handle_async/3
  @impl true
  def handle_async(
        :message_count,
        {:ok, %{count: count, count_i: count_i, count_o: count_o}},
        socket
      ) do
    # Create chart data
    chart_data = total_chart(count, count_o, count_i)

    # Log for debugging
    Logger.debug("Sending chart-totals event with data: #{inspect(chart_data)}")

    socket =
      socket
      |> assign(:message_count, AsyncResult.ok({count, count_o, count_i}))
      |> push_event("chart-totals", chart_data)

    {:noreply, socket}
  end

  def handle_async(:lead_count, {:ok, %{count: count}}, socket) do
    batch = socket.assigns.batch

    socket =
      socket
      |> assign(:lead_count, AsyncResult.ok(count))
      |> start_async(:delivered, fn -> get_stat(batch, :delivered) end)

    {:noreply, socket}
  end

  def handle_async(:delivered, {:ok, %{count: count}}, socket) do
    records = socket.assigns.lead_count.result

    ratio_pct =
      if 0 == records do
        nil
      else
        count / records * 100
      end

    socket =
      socket
      |> assign(:delivered, AsyncResult.ok({count, records, ratio_pct}))
      |> assign(
        delivered_count: count,
        delivered_records: records,
        delivered_ratio_pct: ratio_pct
      )

    {:noreply, socket}
  end

  def handle_async(:decision_breakdown, {:ok, data}, socket) do
    # Create chart data
    chart_data = decision_breakdown_chart(data)

    # Log for debugging
    Logger.debug("Sending chart-response_rate event with data: #{inspect(chart_data)}")

    socket =
      socket
      |> assign(:response_rate, AsyncResult.ok(data))
      |> push_event("chart-response_rate", chart_data)

    {:noreply, socket}
  end

  def handle_async(stat, {:ok, %{count: count}}, socket)
      when stat in ~w[ bounced message_count]a do
    socket =
      socket
      |> assign(stat, AsyncResult.ok(count))

    {:noreply, socket}
  end

  @impl true
  def handle_event("change-tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  @impl true
  def handle_event("show_approver_input", _, socket) do
    # Redirect to the approvals tab
    {:noreply, assign(socket, :active_tab, "approvals")}
  end

  @impl true
  def handle_event("send_batch", _, socket) do
    batch = socket.assigns.batch

    {:ok, total_mgs_sent} = Admin.Messaging.send_sms_batch!(batch, true)

    {:noreply, assign(socket, :active_tab, "approvals")}
  end

  @impl true
  @impl Phoenix.LiveView
  def handle_event("get_approver_responses", %{"approver_num" => approver_num}, socket) do
    responses = get_approver_responses(socket.assigns.batch, approver_num)

    {:noreply, assign(socket, :current_approver_responses, responses)}
    # {:noreply, socket}
  end

  @impl true
  def get_approver_responses(batch, approver_num) do
    #responses =
      #InboundSMS
      #|> Ash.Query.filter(batch_id == ^batch.id and src == ^approver_num)
      #|> Ash.read!()


    #Alternative to response - approval batch_id not set initially on inbound
    ## May include responses from other batches if the same approver spans > 1 around the same time
    responses =
     from(oba in OutboundSMSBatchApproval,
        join: im in InboundSMS,
        on:  oba.phone_number == im.src and oba.sent_at < im.inserted_at,
        where: oba.state != :approved and oba.state != :cancelled and oba.batch_id == ^batch.id,
        select: %{
                  first_name: oba.first_name,
                  last_name: oba.last_name,
                  sent_at: oba.sent_at,
                  state: oba.state,
                  phone_number: oba.phone_number,
                  message: im.message,
                  inserted_at: im.inserted_at,
                  sms_thread_id: im.sms_thread_id
                }
      )|> AdminRepo.Replica.all(prefix: "messaging")

    Logger.debug("inbound_sms approver responses: #{inspect(responses)}")

    responses
  end

  # incomplete -unused
  def get_approver_responses_all(approver_num, socket) do
    socket.assigns.batch_approvers
    |> Enum.each(fn approver -> get_approver_responses(socket.assigns.batch, approver_num) end)
  end

  # MARK: Helpers
  def pretty_number(num) do
    Number.Delimit.number_to_delimited(num, precision: 0)
  end

  # MARK: get_stat/2

  def get_stat(%{lead_file_id: lead_file_id}, :lead_count) do
    count =
      StagedContact
      |> Ash.Query.filter(
        lead_file_id == ^lead_file_id and
          (homephone_line_type == "wireless" or
             altcompanyphone_line_type == "wireless" or
             companyphone_line_type == "wireless" or
             newphone_line_type == "wireless")
      )
      |> Ash.count!()

    %{count: count}
  end

  def get_stat(%{id: id}, :delivered) do
    count =
      OutboundSMS
      |> Ash.Query.filter(batch_id == ^id and state in [:sent])
      |> Ash.count!()

    %{count: count}
  end

  def get_stat(%{id: id}, :bounced) do
    count =
      OutboundSMS
      |> Ash.Query.filter(batch_id == ^id and state in [:error])
      |> Ash.count!()

    %{count: count}
  end

  require Ecto.Query, warn: false
  import Ecto.Query

  def get_stat(%{id: id}, :message_count) do
    count_i =
      from(
        t in SMSThread,
        join: i in InboundSMS,
        on: i.sms_thread_id == t.id,
        where: t.batch_id == ^id
      )
      |> Admin.AdminRepo.Replica.aggregate(:count, prefix: "messaging")

    count_o =
      from(
        t in SMSThread,
        join: o in OutboundSMS,
        on: o.thread_id == t.id,
        where: t.batch_id == ^id
      )
      |> Admin.AdminRepo.Replica.aggregate(:count, prefix: "messaging")

    %{count: count_i + count_o, count_i: count_i, count_o: count_o}
  end

  def get_stat(%{id: id}, :decision_breakdown) do
    # Query threads by decision for this batch
    decision_counts =
      from(t in SMSThread,
        where: t.batch_id == ^id and not is_nil(t.decision),
        group_by: t.decision,
        select: %{decision: t.decision, count: count(t.id)}
      )
      |> Admin.AdminRepo.Replica.all(prefix: "messaging")

    # Query unprocessed threads (those without a decision)
    unprocessed_count =
      from(t in SMSThread,
        where: t.batch_id == ^id and is_nil(t.decision),
        select: count(t.id)
      )
      |> Admin.AdminRepo.one(prefix: "messaging")

    # Add unprocessed to the decision counts if there are any
    decision_counts =
      if unprocessed_count && unprocessed_count > 0 do
        decision_counts ++ [%{decision: "Unprocessed", count: unprocessed_count}]
      else
        decision_counts
      end

    decision_counts
  end

  # MARK: Charts
  def total_chart(_count, count_o, count_i) do
    %{
      chart: %{
        type: "donut",
        width: 150,
        height: 150
      },
      legend: %{
        floating: true,
        offsetY: -45,
        position: "bottom"
      },
      dataLabels:
        %{
          # enabled: true
        },
      series: [count_o, count_i],
      labels: ["Outbound", "Inbound"]
    }
  end

  def responses_chart_actually_works(_who_cars) do
    %{
      series: [
        %{
          name: "Outbound",
          data: [44, 55, 41, 67, 22, 43]
        },
        %{
          name: "Inbound",
          data: [13, 23, 20, 8, 13, 27]
        }
      ],
      chart: %{
        type: "area",
        stacked: true,
        toolbar: %{show: false}
      },
      dataLabels: %{enabled: false},
      stroke: %{curve: "smooth"},
      xaxis: %{
        type: "datetime",
        # Sample from 2025-01-01
        categories: [
          "2025-01-01T00:00:00.000Z",
          "2025-01-01T01:30:00.000Z",
          "2025-01-01T02:30:00.000Z",
          "2025-01-01T03:30:00.000Z",
          "2025-01-01T04:30:00.000Z",
          "2025-01-01T05:30:00.000Z",
          "2025-01-01T06:30:00.000Z"
        ]
      },
      tooltip: %{
        x: %{format: "dd/MM/YY"}
      }
    }
  end

  def decision_breakdown_chart(decision_counts) do
    # Extract series data and labels from decision counts
    series = decision_counts |> Enum.map(fn %{count: count} -> count end)
    labels = decision_counts |> Enum.map(fn %{decision: decision} -> decision end)

    %{
      chart: %{
        type: "donut",
        width: 150,
        height: 150
      },
      legend: %{
        floating: true,
        offsetY: -45,
        position: "bottom"
      },
      dataLabels: %{
        enabled: true
      },
      series: series,
      labels: labels
    }
  end

  # Fetch batch audits for a given batch ID
  defp get_batch_audits(batch_id) do
    try do
      # Use the for_batch action to get audits for this batch
      case BatchAudit.for_batch(batch_id) do
        {:ok, audit} ->
          # If we got a single audit, wrap it in a list
          [Ash.load!(audit, [:selected_by])]

        {:error, _} ->
          # If there was an error or no audit found, return an empty list
          []
      end
    rescue
      # Handle any errors gracefully
      _ -> []
    end
  end
end
