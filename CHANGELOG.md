# Change Log
Notable changes are to be recorded here.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

<!-- changelog -->

## [v1.33.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.32.5...v1.33.0) (2025-09-15)




### Chores / Cleanup:

* dev: vendor in echarts 6.0.0

### Development:

* gorgias: backfiller for shopify customer ids

* bi: get both report libraries rendering

### Features:

* shopify: fetch and store order discount codes

### Bug Fixes:

* storybook: compile storybook separately since they don't import their .js with module support

### Maintenance:

* components are set via storybook.js

* storybook: add stat component to storybook; expand to contain charts

## [v1.32.5](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.32.4...v1.32.5) (2025-09-10)




### Bug Fixes:

* crm: force reads through replica connection

## [v1.32.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.32.3...v1.32.4) (2025-09-10)




### Bug Fixes:

* admin-crm: allow long reads

## [v1.32.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.32.2...v1.32.3) (2025-09-09)




### Bug Fixes:

* dnc: exclude archived leads from selection

## [v1.32.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.32.1...v1.32.2) (2025-09-02)




### Bug Fixes:

* rnd: move source metrics update to report func

## [v1.32.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.32.0...v1.32.1) (2025-09-01)




### Bug Fixes:

* revert back sql attribute

## [v1.32.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.31.0...v1.32.0) (2025-09-01)




### Features:

* rnd: calculate rnd count + update source metrics after rnd

* rnd: added rnd to source_metrics

### Bug Fixes:

* rnd: only valid rnd fields when opt-in

## [v1.31.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.30.4...v1.31.0) (2025-08-26)




### Chores / Cleanup:

* rnd: impl progress

* dev: undo previous addition (not used)

* docs: digitial archaeology

### Features:

* allow archiving of staged contacts in the DB

* rnd: skip phones w/ null dates from selection

* rnd: require rnd opt-in mapped fields

* rnd: add RND category to lead file report

* rnd: convert incoming date fields + other phone_metadata updates + qd devised by year

* dev: additional CAS ticket reverse job (default 3 days ago)

* rnd: functional sftp poll + download + intrepreting of results

* rnd: rough read implementation

* rnd: make connectable

* track more date fields for RND process

### Bug Fixes:

* rnd: update to file for upload

* setups: deeper backtrace

* correct RND fields

* dev: fix dev container build permissions

* dev: fix dev container build

* crm: typo

## [v1.30.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.30.3...v1.30.4) (2025-07-21)




### Quality of Life Improvements:

* merge dexis logic from test branch

## [v1.30.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.30.2...v1.30.3) (2025-07-15)




### Chores / Cleanup:

* dev: update Pat's backlog

* project: update project files

* dev: clean up TODO's that are done or no longer are needed

* dev: clean up auto-gen charts, should hand-craft a helm chart for this

### Bug Fixes:

* data entry: mark as completed exception when exception

* data entry: don't track presence on static render

* docs: include overview.md

### Quality of Life Improvements:

* dev: install languages into their own volume

## [v1.30.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.30.1...v1.30.2) (2025-07-08)




### Bug Fixes:

* ci: always doc even in staging

* Update .gitlab-ci.yml file

### Quality of Life Improvements:

* ci: move next release testing to own branch

## [v1.30.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.30.0...v1.30.1) (2025-07-07)




### Bug Fixes:

* ci: rollback staging otp upgrade â rebuild in a fork

## [v1.30.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.29.0...v1.30.0) (2025-07-07)




### Chores / Cleanup:

* dev: update Pat's backlog

* dev: update livebook

* remove parenthesis for zero arity function definitions

### Features:

* format the **entire** codebase

### Bug Fixes:

* ci: correct default exposted ports

### Quality of Life Improvements:

* dev: create k8s friendly dev-env (WIP)

* make staging use next OTP for testing

## [v1.29.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.28.0...v1.29.0) (2025-07-02)




### Chores / Cleanup:

* codebase-wide: cleanup trailing whitepaces

* gorgias: clenaup credo warnings within gorgias + format

* shopify: cleanup credo warnings within shopify + format

* vscode: don't exclude credo.sarif (aka Pat's backlog)

* vscode: exclude some big directories

* test: format tests

* test: cleanup test warnings

* ui: clean up checkbox inputs and document HTML oddities

* ui: clean up debug output

### Features:

* test: allow async liveview tests that rely upon Admin.AdminRepo

### Bug Fixes:

* shopify: re-create missing customer indexes

* test: fix flakey generator

* data entry: change maybe mark logic to use data from mailer rather than ui stance of user

### Maintenance:

* dev: Update Pat's backlog

### Quality of Life Improvements:

* data entry: add logging field to mailers (TBI)

* data entry: add change user_id

## [v1.28.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.27.4...v1.28.0) (2025-07-01)




### Features:

* add super admin action log

* data entry: add mailer change tracking

### Bug Fixes:

* ui: only include super admin options in staging

## [v1.27.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.27.3...v1.27.4) (2025-06-30)




### Bug Fixes:

* data entry: fix admin time display

### Quality of Life Improvements:

* dev / maint: allow super admins to log in as any user (only in staging)

* auth: auto-load roles

## [v1.27.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.27.2...v1.27.3) (2025-06-30)




### Chores / Cleanup:

* formatting

* llms: update claude approved settings

### Bug Fixes:

* ui: only use flop meta when flopping pages

### Maintenance:

* crm: move crm tools to their own path

* dev: update Pat's backlog

### Quality of Life Improvements:

* crm: add effort index and details

* ui: add flop pagination for ecto resources

## [v1.27.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.27.1...v1.27.2) (2025-06-27)




### Chores / Cleanup:

* test: clean up various tests

* deps: use live view 1.1.0-rc for better tests

### Bug Fixes:

* data entry: protections during leave navigation

* data entry: mark as previewed only when in truncated view

* data entry: ensure account and sign-date saves on full form

* data entry: trap exits

### Quality of Life Improvements:

* data entry: trap exists of presence trackers and pubsub

* data entry: move presence to nano-second for better accuracy

* dev: allow direct user-id login

### UI Changes:

* data entry: show unsaved changes better

## [v1.27.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.27.0...v1.27.1) (2025-06-27)




### Bug Fixes:

* auth: typo

## [v1.27.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.26.1...v1.27.0) (2025-06-27)




### Chores / Cleanup:

* deps: update floki

* docs: Make sure the LLMs & Jada undertands Pat's insanity

* test-planning: Make a document about how much Pat sucks at testing

* test: make test evaluator (aka, talk to a mirror all day)

* test: deprecate some test methods (fixtures)

### Features:

* data entry: add tests for transcription

* test: new Ash.Generator modules

### Bug Fixes:

* data entry: only update status when items have changed

* users: allow more loose avatar urls

* ui: remove bad id

* test: clean up list tests

### Maintenance:

* test: quiet down some output

* test: update generators to Ash.Generators

## [v1.26.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.26.0...v1.26.1) (2025-06-23)




### Bug Fixes:

* data entry: only check for dups / save-conflicts if viewing a mailer

## [v1.26.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.25.0...v1.26.0) (2025-06-23)




### Chores / Cleanup:

* data entry: don't inspect in prod

### Features:

* data entry: qc read only mode

### Bug Fixes:

* data entry: keep track of updated mailers before auto-declaim fires to prevent reverting status to new/previewed

* data entry: many claim related bug fixes and additional error/warning logging

* ecto: update ecto sql to match ecto upgrade

* data entry: multiple small fixes

### Maintenance:

* data entry: increased logging

### Quality of Life Improvements:

* data entry: many duplicate checking improvements, feedback, other-user-save protections, and logging

* data entry admin: better dup checking

* data entry admin: add read-only links, more feedback

## [v1.25.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.24.3...v1.25.0) (2025-06-20)




### Chores / Cleanup:

* deps: update ecto

* transcription: Logger.debug rather than IO.puts

### Development:

* settle down to fewer event spams in console

* re-link livebook folder

* ash: add required ash functions/extensions

### Features:

* transcription: add live presence tracking

### Bug Fixes:

* transcription: require logger

* transcription: add explicit claimed_by_id on top of status based claim

### Quality of Life Improvements:

* transcription: add claimed mailers query shortcut

## [v1.24.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.24.2...v1.24.3) (2025-06-13)




### Bug Fixes:

* transcription: attempt to address rat race issue again (leave existing user alone, notify conflict user, etc.)

## [v1.24.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.24.1...v1.24.2) (2025-06-12)




### Bug Fixes:

* transcription: removed timers

## [v1.24.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.24.0...v1.24.1) (2025-06-12)




### Bug Fixes:

* transcription: major reworking on mailers  viewing - dup issue

## [v1.24.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.23.5...v1.24.0) (2025-06-12)




### Features:

* baking: calculate ns pulls in bakedbysource

### Bug Fixes:

* transcription: possible fix for malier dup issues for concurrent users.

## [v1.23.5](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.23.4...v1.23.5) (2025-06-09)




### Bug Fixes:

* oban: re-enable Oban Supervisor

## [v1.23.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.23.3...v1.23.4) (2025-06-09)




### Bug Fixes:

* setups: perform_dncscrub(s) name change to pick up already scrubbed.

## [v1.23.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.23.2...v1.23.3) (2025-06-09)




### Bug Fixes:

* minor change to release

### Maintenance:

* setups: remove complete cleanup for indiv LFs

* omeda: switch to prod endpoint + accept effort id in do_the_thing()

## [v1.23.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.23.1...v1.23.2) (2025-06-06)




### Bug Fixes:

* omeda api: final correction

* omeda: for real this time

* omeda: fix type id name

* omeda api: typo + config skipped fix

## [v1.23.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.23.0...v1.23.1) (2025-06-06)




### Bug Fixes:

* ui: actually respect browser / system dark/light mode preferences

## [v1.23.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.22.2...v1.23.0) (2025-06-06)




### Features:

* omeda: API PROC

* dev: add ability to export "bundles" for archiving and dev

* auth: add roles to take steps toward RBAC

### Bug Fixes:

* ui: allow people to not be sent to theme purgatory

* crm: fixup dev env crm db schemas

### Maintenance:

* ui: ensure live layout has request path

## [v1.22.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.22.1...v1.22.2) (2025-06-06)




### Bug Fixes:

* ui: ensure all pipelines have assigns initialized

## [v1.22.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.22.0...v1.22.1) (2025-06-06)




### Bug Fixes:

* routes: ensure all routes have a valid pipe_through prefix

### Quality of Life Improvements:

* dnc propagation: include count of dnc's that the DNC resulted during prop

## [v1.22.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.21.0...v1.22.0) (2025-06-06)




### Chores / Cleanup:

* deps: bump tidewave to a pr

### Development:

* add claude helper data

* staged contacts: enable flag to skip triggers (should remain off in dev)

### Features:

* contact engine: consolidate result codes and apply overrides

### Bug Fixes:

* setups review: initial wireless display account for any nn-like policy

* contact engine: update result_code cases + exclude initial wireless display for RQ

* contact engine: prevent dataframe from building a DF without all phone fields

* ui: various local-datetime fixes

* staged contacts: removed fail safe on segment selection

* contact engine: blank phones for voip and landline on crm load

### Maintenance:

* dnc prop log: sync columns back up with with migrations

### Quality of Life Improvements:

* ui: auto un-fold menu items when in that section, or on index.

* crm: return errors from staging contacts

* dnc: store effective result code.

* ui: menu bar tweaks

## [v1.21.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.9...v1.21.0) (2025-06-03)




### Chores / Cleanup:

* docs: lay out split plan

### Development:

* ignore local deps repos for testing edge stuff

### Features:

* make home not be dumb

### Bug Fixes:

* ui: various setup fixes

* crm: keep setup in state throughout processing

* ui: Don't show dashboard links to signed out users

* crm: various setup fixes

* dev: make mysql happy

* transcription: form editor had a bad icon, causing crash

### Maintenance:

* dnc log: add company name to log record

### Quality of Life Improvements:

* crm: make it fancy ð

## [v1.20.9](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.8...v1.20.9) (2025-06-03)




### Development:

* update Pat's backlog

* tone back debug logging in lists

* tone back some debug logging

### Bug Fixes:

* crm: temp disable prompt system within process

* crm: temp disable the group calling prompt system

* crm: accept migrated scrub url param

* contact engine: other way around

* contact engine: apply wireless linetype based on ebr

* crm: query timeouts for dnc propagation

* setups: source_metrics voip to use result_code

### Maintenance:

* lead file report: add invalid number as a reason

* logs: tone back some logging

### Quality of Life Improvements:

* ui: tighten things up, increase links between setups and lead files

## [v1.20.8](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.7...v1.20.8) (2025-06-02)




### Chores / Cleanup:

* dev: update pat's backlog

### Quality of Life Improvements:

* crm: expand the dnc propagation log

* lead files: better index functionality; handle direct downloads.

## [v1.20.7](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.6...v1.20.7) (2025-06-02)




### Bug Fixes:

* contact engine: fix warning, contact id not available there

## [v1.20.6](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.5...v1.20.6) (2025-06-02)




### Bug Fixes:

* setups: exclude voip from initial wireless count + moved exclude_result_codes definition

* contact engine: match off of result code rather than line type

* setups: spelling voip wrong  - sticking to it

* contact engine: typo

* contact engine: force early DNC of wireless in no wireless state (according to DNCScrub)

* setups: add voip to landline count

* contact engine: set line type to Uncallable when DNC for backwards compat + reporting

* setups: return mapping after debug, removed extra length/1 call

## [v1.20.5](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.4...v1.20.5) (2025-06-02)



### Notes:
CSV needs a major version bump that includes breaking changes. We need to build out some tests around the modules that use CSV before we update it.

### Chores / Cleanup:

* deps: update deps where possible

### Bug Fixes:

* jobs: added missing require Logger

* gorgias: added missing alias for CSAT loader

### Quality of Life Improvements:

* gorgias: add identity for ticket tags for faster lookup during sync

## [v1.20.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.3...v1.20.4) (2025-06-02)




### Bug Fixes:

* gorgias: ensure stop_at is sane for forward vs reverse loading

## [v1.20.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.2...v1.20.3) (2025-06-02)




### Bug Fixes:

* setups: restore nuked setup metrics, oops

## [v1.20.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.1...v1.20.2) (2025-06-02)




### Chores / Cleanup:

* docs: handle some of the backlog

* dev: update Pat's backlog of chaos

### Bug Fixes:

* contact engine: only count non-empty phones

### Maintenance:

* docs: tidy up docs

* code base wide: replace all instances of IO.inspect/1 & /2 with Logger calls

### Quality of Life Improvements:

* setups: add leadfile report to setup show page

* crm: allow quick DNC downloads

* dev: disable shell integration for faster git

## [v1.20.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.20.0...v1.20.1) (2025-05-31)




### Chores / Cleanup:

* dev: update live_debugger tags

### Bug Fixes:

* contact engine: apply backwards compatability for source metrics

* lead file processor: wrong second scrub type; code_nn was nil

### Quality of Life Improvements:

* setups: merge sidebar logic; allow live updates + resuming of held lead files

* setups: allow pausing after scrub

## [v1.20.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.14...v1.20.0) (2025-05-30)




### Chores / Cleanup:

* contact engine: refactor / rearrange module

* contact engine: clarify notes / docs

### Features:

* bubble up litigator detection

* lead file reporting with breakdown

* create the ContactEngine, and utilize it for DNC logic

### Bug Fixes:

* contact engine: missing alias and typo

## [v1.19.14](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.13...v1.19.14) (2025-05-29)




### Bug Fixes:

* gorgias: missing alias

## [v1.19.13](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.12...v1.19.13) (2025-05-29)




### Bug Fixes:

* gorgias: upsert on create, not changeset

* abandoned call to saved_mappings after transplant to Setups module

## [v1.19.12](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.11...v1.19.12) (2025-05-28)




### Chores / Cleanup:

* crm: refactor mapping functions to Setups

### Bug Fixes:

* gorgias: force ticket upsert

* crm: reference migrated functions

### Quality of Life Improvements:

* crm: expand NPA validation / matching logic

* crm: Add bulk metadata for all phones

## [v1.19.11](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.10...v1.19.11) (2025-05-28)




### Chores / Cleanup:

* gorgias: bump log level to info, to get needed insights from continuation eval

## [v1.19.10](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.9...v1.19.10) (2025-05-27)




### Bug Fixes:

* gorgias: bad stop at comparison

### Quality of Life Improvements:

* crm: provide easy access to NPA cache and parsing

## [v1.19.9](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.8...v1.19.9) (2025-05-27)




### Bug Fixes:

* transcription: change default, since it's an active check

### Quality of Life Improvements:

* transcription: be able to list active users, for debugging

## [v1.19.8](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.7...v1.19.8) (2025-05-26)




### Bug Fixes:

* transcription: add timestamps, only declaim older than previous run.

### Quality of Life Improvements:

* transcription: add mailer id copy shortcut

## [v1.19.7](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.6...v1.19.7) (2025-05-24)




### Bug Fixes:

* gorgias: decode cursor for `continue?` logic

* ref missing from staging

## [v1.19.6](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.5...v1.19.6) (2025-05-23)



### Quality of Life Improvements:

* gorgias: add `enqueue_reverse_update/2` to the Loader Workers

### Chores / Cleanup:

* ci: update gitops keys

### Bug Fixes:

* transcription: update checkbox value handling & properly handle account field via full form

* gorgias: allow inserting records where we don't (yet) have the related object

* gorgias: rewrite / refactor to account for edge-cases

### Maintenance:

* crm: added phone metadata to staged contacts

## [v1.19.5](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.4...v1.19.5) (2025-05-21)




### Development:

* deps: update live debugger, away from github

### Bug Fixes:

* gorgias: be more patient

* webhooks: propagate dial id from contact id

* ui: only display ref in staging

## [v1.19.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.3...v1.19.4) (2025-05-20)




### Bug Fixes:

* show git sha in staging

## [v1.19.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.2...v1.19.3) (2025-05-20)




### Bug Fixes:

* sms: clarify state rejections

## [v1.19.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.1...v1.19.2) (2025-05-19)




### Bug Fixes:

* ci: update gitops config

### Quality of Life Improvements:

* add version to footer

## [v1.19.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.19.0...v1.19.1) (2025-05-19)




### Bug Fixes:

* docs: include ex_doc so we can build docs from prod code

## [v1.19.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.18.0...v1.19.0) (2025-05-19)




### Features:

* allow gorgias multi-tenancy

* add gorgias tenant table

* CRM Projects, functional(ish) but still a WIP

### Bug Fixes:

* loaders: property drill tenant

* loaders: missed tenant in some gorgias loaders

* setup_recovery: handle empty loads (steps count)

## [v1.18.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.17.0...v1.18.0) (2025-05-16)




### Features:

* wrap up NPA importer

### Bug Fixes:

* update edit_mappings in validate

* setups: make column fetch async

## [v1.17.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.16.0...v1.17.0) (2025-05-15)




### Development:

* try the new official mcp

### Features:

* npa automation + testing

* sms approval/send ui: approvals (add, delete, notify, view response/s, approve)

* add NPA parser with 100% test coverage!

* setup tool recovery revamp (disanble/renable staging triggers, step by step feedback, indiv leadfile recovery, UI update)

* add NPA mapping for more detailed state referencing

* allow _W append to wireless sourcecodes

* allow wireless leads on landline + handle both segments in one child

* added cascading new fields assignment in default table mappings.

* add *rough draft* of Crm Projects

### Bug Fixes:

* ci: bad folder permissions

* trim incoming for heading replacements + handle files without homepostcode

* handle diff params in upload validate

* sms-ui: update charts when swaping to a different batch

## [v1.14.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.13.1...v1.14.0) (2025-05-02)




### Features:

* add *rough draft* of Crm Projects

### Bug Fixes:

* sms-ui: update charts when swaping to a different batch

## [v1.13.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.13.0...v1.13.1) (2025-05-01)




### Bug Fixes:

* sms: refresh audit batches and close modal on save

* Org menu item name was duplicated

* index component tests

* sms: nav buttons

* sms: missing buttons

* sms: sync audit search

* add rate limit accomodations to openai + testing

### UI Changes:

* sms: show user avatar

* make search toggable

* expand sms qc ui

## [v1.13.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.12.0...v1.13.0) (2025-04-30)




### Features:

* add classify batch shortcut

## [v1.12.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.11.0...v1.12.0) (2025-04-30)




### Features:

* sms: quicklink to batch audit

### Quality of Life Improvements:

* sms: add batch audit display

## [v1.11.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.10.5...v1.11.0) (2025-04-30)




### Features:

* changeup the refund walker to a standard oban job.

* add lead file index (TODO: Add detail)

### Maintenance:

* sms: change up default confidance level, adapt to openai schema limitations

### Quality of Life Improvements:

* sms: include batch in sms inbound

## [v1.10.5](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.10.4...v1.10.5) (2025-04-29)




### Bug Fixes:

* ci: don't double copy + limit layers

## [v1.10.4](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.10.3...v1.10.4) (2025-04-29)




### Bug Fixes:

* ci: dockerfile chaos

## [v1.10.3](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.10.2...v1.10.3) (2025-04-29)




### Bug Fixes:

* ui: bad workdir

## [v1.10.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.10.1...v1.10.2) (2025-04-29)




### Bug Fixes:

* ci: don't switch user until things

## [v1.10.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.10.0...v1.10.1) (2025-04-29)




### Bug Fixes:

* ci: don't home via root

## [v1.10.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.9.2...v1.10.0) (2025-04-28)




### Features:

* return a normal user to the deployment pipeline

## [v1.9.2](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.9.1...v1.9.2) (2025-04-28)




### Bug Fixes:

* include DLX exchange

* ci: tag staging builds properly

## [v1.9.1](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.9.0...v1.9.1) (2025-04-28)




### Bug Fixes:

* ci: allow docs to fail to build

## [v1.9.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.8.0...v1.9.0) (2025-04-28)




### Features:

* add real-time updates between SMS QC + Thread views

## [v1.8.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.7.0-6fdf726c...v1.8.0) (2025-04-28)




### Development:

* add factories for messaging

### Features:

* dev: add ability to mock inbound messages

## [v1.7.0-6fdf726c](https://gitlab.gad-inc.com/pstallings/api_umbrella/compare/v1.6.0...v1.7.0-6fdf726c) (2025-04-25)




### Development:

* only access intercepted SMS messages from dev/test

* migrate local_backups dir to avoid bloaded local releases

* disable YesterdayTodayRebake, add [Admin, Landline, Wireless] repos to oban

* somewhat html formatted js copy

* save counts for added scrub categories

* additiona scrub result categories added to SourceMetrics

* unless is now deprecated

* add igniter config

* rename columns rather than hard move

* don't reload then reload again because tailwind fired.

* don't pry by default, dbg itself is super useful now.

* try augment?

* added some snippets

* try unpinning picosat, I think the bug is patched

* correlate

* tie LiveView Debugger into js land state

* init threads ui

* update language versions

* update core langage

* update some deps

* don't io debug in prod

* update some deps

* add local dev openai access

* upgrade to postgres w/ ai support + docs update

* add leadfilemapping table resource to DB

* update sarif backlog

* qol

* access local keys

* add comment for migration

* add sarif as default extension

* rename file to match module name

* handle errors better and fixup reporting.

* jobs staging changes

* prep for upcoming jobs staging change

* daily rebake

* merge from staging

* clean up warnings and notes

* route change to show module, tab stage route add

* convert show to live component + tab index by stage

* add local backup dir and docs to switch around DBs

* list counts for review step

* review step logic + UI add

* group optin prompt UI + updates to handle prompt response and load_record broadcasts

* group eligibility check, completed set up reg + group_calling process (w or w/o prompt)

* update sms poc

* check in poc changes

* helper scripts

* sms process updates

* revive livebook, plumb into container env

* schema alterations

* public attribute changes (ASH)

* actor changes for ash

* bugfixes

* added run logged, tweaked some bulk inserts

* sync model changes after migration

* add paper trail and archival

* update resource diagrams

* add paper trail

* actor bug-fixes

* changes according to the maint docs

* patch work, to be changed

* add reminders to add validation

* bugfix, new form after save

* better vscode env

* awesome tracing ðµï¸ââï¸

* merge devcontainers to staging

* add default extensions and configure tailwind folding

* fix mysql ports

* add url shortener script

* refactor DNCScrub code for reuse

* schema nil changes + more staged contact fields

* visualize, with your mind ð§ (and svg)

* add SMS poc livebook

* start of job options

### Features:

* add oban web panel

* add intercepted message UI

* intercept SMS messages during local development to prevent API usage in dev.

* include docs in prod releases

* add lead creation and callable consumers

* gorgias ticket custom fields pull (custom_fields, ticket_custom_fields)

* disabled invalid state removals (doesn't account for Canada)

* fetch + display scrub detials, initial w conut (nn)

* set preprocess notes (schema + phone metrics changes)

* ui: add charts ð

* sms: selection work

* sms: sms qc tweaks, audit selection draft

* add confidence to classifier

* sms: retain batch audit settings used.

* pre-processing - special state removals (logic, ui, migrations)

* sms: add check

* setup pre-processing - zipcode clean/padding 2

* setup pre-processing - zipcode clean/padding

* virtualize the different decision columns

* add batch audits

* ui: add chat storybook for styling

* dev: send elixir console to browser console in dev

* ui: fully functional hotkey engine

* ui: hotkey revap for sms qc

* ui: "minor" UI changes for tailwind v4

* ui: Tailwind CSS V4, again

* ui: login as some ids. maybe we can add a picker if needed.

* ui: add vega lite and global hotkeys, but no reports or shortcuts, muahahahaha

* file pre-processing update (replace state w/ abbrev, invalid state/phone removals)

* destroy saved mapping for removed LF + reload socket on upload/save/removal

* added mapped headings replacement to setup process

* field mapping UI (save + edit + validate)

* auto field mapping and saving

* implement user settings

* update storybook

* update devcontainer

* add user preferences

* start of dark mode

* call me a maniac, but I templated the index page...

* make menus open/close all fancy ð

* update to phoenix 1.0.0-rc.0 (upgrading in stages)

* support middle text (search comment)

* qol improvements

* add campaign pagination too

* add good looking pagination to core components

* auto correlate and auto classify

* sms correlation shortcuts

* OpenAI skel

* loader_record_editor validations

* new parent form validations

* tested dnc scrub project dnc tools

* tie out approvals with their message threads

* merge local dev of approvals to staging

* draft of approvals, to change with UI

* draft of EBR endpoint skel, but going tdd

* upgrade tesla to access `Tesla.Test`

* api init

* Setup Cleanup -> Added UI logic to 'Process' stage

* Setup Cleanup -> Handle Plan Stage Jobs + DNCScrub Re-entry

* Setup Cleanup -> CRM functions

* animate some things, standardize to a reusable block

* schedule at setup time

* sms tweaks

* more sms linking

* sms batch auto creation

* incorporate sms batches directly into setup tool

* starter of template work

* add sms columns to load record

* cross links between SMS and LeadFiles

* correlate sms messages

* fields for forward correlation

* sms thread work

* setup process QOL; always scroll down

* more SMS batch UI, and tweaks to the setup UI

* QOL UI work

* sms batch work

* model changes for assisted recovery

* potential setup recovery methods (unfinished)

* Finish CID lookup tool w/ logging

* show options added (edit,discard, archive) + details dropdown

* prompt update to handle concurrent broadcasts + handling leadfile stage updates

* add opt-in fields to contacts for import

* merge crm-bits into staging

* TS-119 loader works to contact, need to sort group calling

* local wireless base campaigns

* some vicidial schemas and migrate cloning logic to work with schemas when feasible

* vicidial schemas start, added campaigns

* TS-119 loader adjustments, show overall plan when moving forward

* TS-119 assignment UI tweaks

* start of operations work

* TS-112 add dialer to UI

* TS-112 plumb path back together

* TS-119 auto complete name based on filename

* TS-119 merge all experiments into rough draft

* added plan page

* more devcontainer work

* fix admin_dev init db

* local dev env work

* local .devcontainer

* progress on loader, local branch doesn't compile atm

* migrate to Admin.Oban + upgrade queue count for crm

* dnc scrub interpretation progress

* don't rescrub files

* permanently retain DNC scrub results

* lead files to staged with dnc results in db!!

* download, unpack and ready dnc scrub result for loading.

* add lead staging with failsafes

* formatter work and outbound sms tweaks

* progress on job options in load

* allow filtering by active forms

* upload and poll api for dnc scrub

* output from jobs

* make it fancy ð

* finish upload page

* setup file load page work

* track latest job

* spawn jobs on upload confirmation

* setup UI work

* checking in local work

* add tests to setup upload

* add lead_file_contacts

* upload setup page

* new setup page

* db migrations for setups

* much setup work

* route planning

* starter ui+reporting schema

* migrate projects context to Admin.Crm (more to move)

* refactor new pages

* start of crm functionality

### Bug Fixes:

* sms: migrate to utc datetime & handle interception better

* ci: wrong build dir

* ci: wrong workdir

* ci: wrong output dir

* no longer invading ticket blob

* add math

* single quote

* safe migrations

* dark mode is unfinished, ignore systems that want it. Opt in only.

* stray comma

* selected atoms in sourcemetrics

* these values are UTC, we don't need naive dt

* wrong label for hotkeys

* ui: fix some of the padding issues, and missing forms plugin.

* ui: disable vegalite in main js, make it optional.

* setups search selection disappearing act

* setup index switched back to created_by + misc ui

* line_type  + sourcemetrics update

* add batch_id on thread create

* wireless nn blocked - uncallable

* result_code vs result_code_nn line_type conflict

* ui/color fixes

* only update some values on conflict here.

* various UI bugs

* tiny menu clickboxes

* wrong generic user field, wrong tab prefix

* various model shortcut errors

* reset streams when setting items

* display bug in crm

* fix liveview reconnecting on all of these links

* don't fetch batches twice

* update to new internal opts name

* use new wireless

* adjustments

* scrub results and _dnc? counts

* don't allow unique running bakes

* setup metrics

* be able to run prod locally for migrations

* send not tie

* some builds might not have the fwf table

* Form format changed

* move effortproject delete to parent func instead of child

* add contacts destaging to recovery process

* left disabled from dev

* split Oban queues

* inverted logs and too many loops

* remove overflow-hidden when visible.

* multiple sources in leadfile metrics

* typo

* overzelaous Ash validation

* static SMS sender if not provided

* handle sources without groups

* review wireless parent condition

* redo review project retrieval

* handle all on wireless correctly.

* sourcecode heading

* project max attempts updates

* CrmMapping update

* load record broadcast fix

* module acceptances / defaults / skip_unknown_params

* bad schema name

* don't double encode

* catch staging errors

* don't double encode messages

* prod changes downstream

* prod fixes from downstream

* don't range bake XDIAL, it'll die

* app not starting because dotenv expects :automatic for safety

* use correct ash API

* clarify

* don't crash

* merge fix from main

* Use CRM Projects

* don't require a class

* stop tracking removed lead files

* don't crash loop

* menu items

* bug

* don't start Admin supervisor in staging AS#8

* bad link to campaigns

* don't bring elixirsense into prod

* move to campaigns

* include ecto sandbox (I hate it)

* cicd error

### Maintenance:

* update warnings

* ui updates, setup edit popup fix

* mapping warnings + dark-mode ui changes

* slight mapping validation update

* add numbers for pretty displays ð

* minor UI updates

* LeadFileMapping upsert update + Validation module

* Don't schedule jobs in staging

* update menu items

* Remove client side icon handling, use svg provided by library

* remove deprecated option

* simplify

* prep for ui; load option defaults

* remove useless ThreadMessage object

* clean up warnings

* stop job explosions

* solidify max_runtime_minutes to prevent dual scheduling daily

* report code lines in errors

* fixup id

* remove unused correlator

* add reset action handle info

* slight dup_numbers change

* update pats backlog

* supp to be safe

* staging timeout to infinity + LF set to reset transition

* found bug

* bugfix

* update credo backlog

* upgrade oban jobs table

* slight ui change

* docs

* more meta-data on failure

* some of the EX5006's cleared, don't put in prod

* strict sarif backlog

* format dnc_scrub.ex + rebuild sarif backlog

* allow TXT, it's just a csv 99% of the time

* Oban in Test, Dev, Prod when first node and not staging

* merge conflic kept old record

* only run oban on one node

* fixup creates that already exist

* handle flat :ok atom

* not needed

* working through the warning/error backlog

* add credo.sarif backlog and start some cleanup

* don't spam IO.* in prod

* more robust error/timeout handling

* Oban config fixes

* abort Oban in staging, still causing issues

* revert logger handling, causing some strange issues

* don't collate locally, only the prod server has a funky encoding.

* restore legacy loader & update crm@container-local configs (LOCAL CRM REBUILD REQUIRED!)

* handle already started errors (indicator of double run?)

* formatting

* missed an unkown attribrute

* correct metrics models for Ash 3.0

* setup_metrics job alterations

* cleanup display & don't require atomic updates

* more missing actors

* add-back missing actors

* load either or segment logic + misc

* updated review output

* refactor output

* start Oban in staging

* fixup sample data

* review output cleanup + adjustment

* revert race condition change; Was an Oban bug

* update only oban

* attempt to prevent a race condition

* log caught generic to appsignal for investigation

* format error with all info

* source_code add to select + list threshold removal

* handle project nils

* don't dbg in prod and version bump

* correct oban time (was 7,000 days due to ms)

* version bump

* finish bubbling up job prefix changes

* swap to appuser from nobody

* correct ssh-client without server

* attempt to fix ssh

* bug fixes

* minour updates

* pat: target file wait time + contact field trimming

* exclude discarded and archived setups from list + dncscrub results grab for first tab w/o click

* core components ui updates

* ui updates

* already group calling project check + misc

* tab + serch component updates

* switch setup search Api call

* update child with max_attempts, add group + container projs w/ max_attempts and dialer

* analyze step time shorten [only stage phone fields, leadfile_id query filter]

* stage updates + group prompt update

* ignore inserted_at and updated_at insert because of conflict

* force upgrade tzdata to skip bugged version

* schema changes to match local changes

* warnings and reminders

* missed atomic updates

* merge changes from staging/prod downstream

* CrmMapping changes (add columns)

* private? false -> public? false default changes

* it now compiles

* remove registries

* check-in progress

* cleanup warnings

* add landlind load empty state

* clean up duplicated IDs on forms

* add some guards and default to absent

* notes on baked responses sa vs vs

* typo

* nightly refresh for

* fmt

* small changes

* split console gathering directly to job outputs

* mix format

* update max attempt defaults

* dnc sftp debugging

* don't puts in prod

* move test files

* disable as a test

* remove todo; already working, bad sample set

* ready for next round of changes
## [v0.1.3]

- Added new first-class Lead Loader / Setup tool
- Added first-class SMS support # TODO: Finish UI for SMS, and backend flow progress
- Added Opt-In style columns on Crm.Contact to support client lists that already contain that information
- - SmsOptIn: DNP style fields must be inverted
- - EmailOptIn: DNP style fields must be inverted

## [v0.1.2]

#### TODO: Must finish before release

- migrate Admin.Crm content into Crm.
- CRM Contact Page (Not intended for full release)
- CRM Project Page
- - Index w/ search
- - Detail / Show
- - - Links to Dialer
- - - Links to Ticket (Wishlist Item)
- - - Quick Dialer / Report Links / Actions (Wishlist Item)
- - Basic Re-usable Edit Modal
- Project **Core Components** style components (Wishlist Item)

#### Added

#### Removed

#### Changed

#### Fixed

## [v0.1.1]

Many untracked changes prior to this.

### New pages

Jada added a group calling reset page, complete with back-end logic.
The page can be accessed from `/crm/campaign/group_leads_reset`.

### Environment Changes

The Sentry config has been dead for a while, it has officially been removed
from `mix.exs`, but please leave it configured in your `.env` file.
We may go back to it.

### Changelog

#### Added

- Group Calling Reset Page
- AppSignal tracing as a trial; will likely continue

#### Removed

- Sentry is no longer configured

#### Fixed

- Inbound SMS Messages should no longer loop in failure (*patched in prod*)
- Correctly identify staging; don't modify env after compile
- `live_reload` not reloading when `lib/dialer` or `lib/crm` projects where changed

#### Changed

- Some CICD build scripts; now use prod.sh / staging.sh to build & push from jumpbox
- Upgrade dependencies: nothing of note
- `live_reload` config changes to enable iex output stream to browser in dev (less alt-tab). Note:
  this does not appear to be working.

## [v0.1.0](https://gitlab.gad-inc.com/pstallings/api_umbrella/-/compare/main...v0.1.0) (2024-01-19)
Start of version tracking.

Project is already live and highly depended upon.
## Changelog begins
