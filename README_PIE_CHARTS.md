# ECharts Pie Chart Component for Phoenix LiveView

A comprehensive, production-ready pie chart component built on top of ECharts for Phoenix LiveView applications.

## 🚀 Features

- **Multiple Chart Types**: Basic pie, donut, rose (nightingale), and nested charts
- **Real-time Updates**: Dynamic data updates via LiveView events
- **Responsive Design**: Automatic resizing and mobile-friendly
- **Theming Support**: Built-in themes (default, walden, dark) and custom styling
- **Interactive**: Hover effects, tooltips, and click events
- **Performance Optimized**: Efficient rendering and memory management
- **Accessibility**: Screen reader friendly with proper ARIA labels
- **TypeScript Ready**: Full type definitions for configuration options

## 📁 Files Created

### Core Component
- `lib/admin_web/components/pie_chart.ex` - Main pie chart component
- `test/admin_web/components/pie_chart_test.exs` - Comprehensive test suite

### Storybook Integration
- `storybook/components/pie_chart.story.exs` - Interactive component showcase

### Examples and Documentation
- `lib/admin_web/live/examples/pie_chart_live.ex` - Complete LiveView example
- `docs/PIE_CHART_IMPLEMENTATION_GUIDE.md` - Detailed implementation guide

### Enhanced JavaScript Hook
- Enhanced `assets/js/hooks/echart_hook.js` with:
  - Dynamic data updates
  - Responsive resizing
  - Better error handling
  - Memory leak prevention

## 🎯 Quick Start

### 1. Basic Usage

```elixir
<.pie_chart 
  id="my-chart"
  title="Sales Distribution"
  data={[
    %{name: "Product A", value: 335},
    %{name: "Product B", value: 310},
    %{name: "Product C", value: 234}
  ]}
/>
```

### 2. Donut Chart

```elixir
<.pie_chart 
  id="donut-chart"
  title="Revenue Breakdown"
  variant="donut"
  inner_radius="40%"
  data={@revenue_data}
/>
```

### 3. Dynamic Updates

```elixir
def handle_event("update_chart", _params, socket) do
  new_data = fetch_updated_data()
  
  socket = 
    socket
    |> assign(:chart_data, new_data)
    |> push_event("chart-update", %{
      chart_id: "my-chart",
      data: new_data
    })

  {:noreply, socket}
end
```

## 🎨 Chart Variations

### Basic Pie Chart
- Standard circular pie chart
- Customizable radius and center position
- Optional labels and legend

### Donut Chart
- Hollow center design
- Configurable inner and outer radius
- Perfect for displaying totals in center

### Rose Chart (Nightingale)
- Radius varies by data value
- Excellent for comparing magnitudes
- Visually striking presentation

### Nested Chart
- Multiple data series
- Hierarchical data visualization
- Configurable radius ranges

## 🎭 Theming Options

### Built-in Themes
- **Default**: Standard ECharts color palette
- **Walden**: Blue-green professional theme
- **Dark**: High contrast dark theme

### Custom Styling
```elixir
data = [
  %{name: "Success", value: 85, itemStyle: %{color: "#10b981"}},
  %{name: "Warning", value: 10, itemStyle: %{color: "#f59e0b"}},
  %{name: "Error", value: 5, itemStyle: %{color: "#ef4444"}}
]
```

## 📊 Real-time Integration

### WebSocket Updates
```elixir
def handle_info({:chart_update, new_data}, socket) do
  socket = push_event(socket, "chart-update", %{
    chart_id: "live-chart",
    data: new_data
  })
  
  {:noreply, socket}
end
```

### Periodic Refresh
```elixir
def mount(_params, _session, socket) do
  if connected?(socket) do
    :timer.send_interval(5000, self(), :update_metrics)
  end
  
  {:ok, assign(socket, :metrics, fetch_metrics())}
end
```

## 🔧 Configuration Options

| Attribute | Type | Default | Description |
|-----------|------|---------|-------------|
| `id` | string | required | Unique chart identifier |
| `title` | string | nil | Chart title |
| `data` | list | required | Chart data points |
| `variant` | string | "pie" | Chart type |
| `theme` | string | "default" | Color theme |
| `width` | integer | 400 | Chart width (px) |
| `height` | integer | 300 | Chart height (px) |
| `radius` | string | "50%" | Chart radius |
| `show_legend` | boolean | true | Display legend |
| `legend_position` | string | "right" | Legend placement |
| `animation` | boolean | true | Enable animations |

## 🧪 Testing

Run the test suite:

```bash
mix test test/admin_web/components/pie_chart_test.exs
```

The test suite covers:
- Component rendering
- Configuration building
- Data formatting
- Error handling
- Edge cases

## 📖 Storybook

View interactive examples in Storybook:

```bash
mix phx.server
# Navigate to /storybook
```

The storybook includes:
- All chart variations
- Different themes
- Loading states
- Custom styling examples

## 🚀 Performance Tips

1. **Limit Data Points**: Keep datasets under 100 points for optimal performance
2. **Update Frequency**: Avoid updates more than once per second
3. **Memory Management**: Charts automatically clean up on component destruction
4. **Responsive Design**: Charts automatically resize with container

## 🐛 Troubleshooting

### Chart Not Rendering
- Verify EChartHook is imported in `app.js`
- Check container has explicit dimensions
- Ensure data format is correct

### Updates Not Working
- Confirm chart ID matches component ID
- Check browser console for errors
- Verify push_event syntax

### Performance Issues
- Reduce update frequency
- Limit data points
- Use appropriate chart variant

## 📚 Additional Resources

- [ECharts Official Documentation](https://echarts.apache.org/en/index.html)
- [Phoenix LiveView Guide](https://hexdocs.pm/phoenix_live_view/)
- [Component Implementation Guide](docs/PIE_CHART_IMPLEMENTATION_GUIDE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Update documentation
5. Submit a pull request

## 📄 License

This component is part of the AdminWeb application and follows the same licensing terms.

---

**Built with ❤️ using Phoenix LiveView and ECharts**
