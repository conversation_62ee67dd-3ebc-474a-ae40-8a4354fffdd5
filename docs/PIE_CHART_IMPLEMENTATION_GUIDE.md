# Pie Chart Implementation Guide for LiveView

This guide provides comprehensive instructions for implementing pie charts in Phoenix LiveView using the ECharts-based pie chart component.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Component Overview](#component-overview)
3. [Chart Variations](#chart-variations)
4. [Dynamic Data Updates](#dynamic-data-updates)
5. [Styling and Theming](#styling-and-theming)
6. [Real-time Integration](#real-time-integration)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting](#troubleshooting)

## Quick Start

### 1. Basic Implementation

```elixir
# In your LiveView module
defmodule MyAppWeb.DashboardLive do
  use MyAppWeb, :live_view
  alias AdminWeb.Components.PieChart

  def mount(_params, _session, socket) do
    data = [
      %{name: "Desktop", value: 1048},
      %{name: "Mobile", value: 735},
      %{name: "Tablet", value: 580}
    ]
    
    {:ok, assign(socket, :traffic_data, data)}
  end

  def render(assigns) do
    ~H"""
    <PieChart.pie_chart
      id="traffic-chart"
      title="Traffic Sources"
      data={@traffic_data}
      width={400}
      height={300}
    />
    """
  end
end
```

### 2. Required Dependencies

Ensure your `assets/js/app.js` includes the EChart hook:

```javascript
import { EChartHook } from "./hooks/echart_hook"

let Hooks = {
  EChartHook: EChartHook,
  // ... other hooks
}
```

## Component Overview

### Core Attributes

| Attribute | Type | Default | Description |
|-----------|------|---------|-------------|
| `id` | string | required | Unique identifier for the chart |
| `title` | string | nil | Chart title |
| `subtitle` | string | nil | Chart subtitle |
| `data` | list | required | Chart data points |
| `variant` | string | "pie" | Chart type: "pie", "donut", "rose", "nested" |
| `theme` | string | "default" | Color theme: "default", "walden", "dark" |
| `width` | integer | 400 | Chart width in pixels |
| `height` | integer | 300 | Chart height in pixels |

### Data Format

Data should be provided as a list of maps with `name` and `value` keys:

```elixir
data = [
  %{name: "Category A", value: 100},
  %{name: "Category B", value: 200},
  %{name: "Category C", value: 150}
]
```

For custom styling, add `itemStyle`:

```elixir
data = [
  %{name: "Success", value: 85, itemStyle: %{color: "#10b981"}},
  %{name: "Failed", value: 15, itemStyle: %{color: "#ef4444"}}
]
```

## Chart Variations

### 1. Basic Pie Chart

```elixir
<PieChart.pie_chart
  id="basic-pie"
  title="Sales Distribution"
  data={@sales_data}
/>
```

### 2. Donut Chart

```elixir
<PieChart.pie_chart
  id="donut-chart"
  title="Revenue Breakdown"
  variant="donut"
  inner_radius="40%"
  radius="70%"
  data={@revenue_data}
/>
```

### 3. Rose Chart (Nightingale)

```elixir
<PieChart.pie_chart
  id="rose-chart"
  title="Performance Metrics"
  variant="rose"
  data={@performance_data}
  theme="walden"
/>
```

### 4. Nested Pie Chart

```elixir
<PieChart.pie_chart
  id="nested-chart"
  title="Hierarchical Data"
  variant="nested"
  inner_radius="30%"
  outer_radius="60%"
  data={@nested_data}
/>
```

## Dynamic Data Updates

### Method 1: Using push_event

```elixir
def handle_event("update_chart", _params, socket) do
  new_data = fetch_updated_data()
  
  socket = 
    socket
    |> assign(:chart_data, new_data)
    |> push_event("chart-update", %{
      chart_id: "my-pie-chart",
      data: new_data
    })

  {:noreply, socket}
end
```

### Method 2: Using assign and re-render

```elixir
def handle_event("refresh_data", _params, socket) do
  new_data = fetch_updated_data()
  {:noreply, assign(socket, :chart_data, new_data)}
end
```

### Real-time Updates with PubSub

```elixir
def mount(_params, _session, socket) do
  if connected?(socket) do
    Phoenix.PubSub.subscribe(MyApp.PubSub, "chart_updates")
  end
  
  {:ok, assign(socket, :data, initial_data())}
end

def handle_info({:chart_update, new_data}, socket) do
  socket = 
    socket
    |> assign(:data, new_data)
    |> push_event("chart-update", %{
      chart_id: "live-chart",
      data: new_data
    })
    
  {:noreply, socket}
end
```

## Styling and Theming

### Built-in Themes

1. **Default Theme**: Standard ECharts colors
2. **Walden Theme**: Blue-green color palette
3. **Dark Theme**: Dark background with bright colors

```elixir
<PieChart.pie_chart
  id="themed-chart"
  data={@data}
  theme="walden"
/>
```

### Custom Colors

```elixir
data = [
  %{name: "Success", value: 85, itemStyle: %{color: "#10b981"}},
  %{name: "Warning", value: 10, itemStyle: %{color: "#f59e0b"}},
  %{name: "Error", value: 5, itemStyle: %{color: "#ef4444"}}
]
```

### Legend Positioning

```elixir
<PieChart.pie_chart
  id="legend-chart"
  data={@data}
  legend_position="bottom"  # "top", "bottom", "left", "right"
/>
```

### Label Customization

```elixir
<PieChart.pie_chart
  id="label-chart"
  data={@data}
  show_labels={true}
  label_position="inside"  # "outside", "inside", "center"
/>
```

## Real-time Integration

### Periodic Updates

```elixir
def mount(_params, _session, socket) do
  if connected?(socket) do
    :timer.send_interval(5000, self(), :update_metrics)
  end
  
  {:ok, assign(socket, :metrics, fetch_metrics())}
end

def handle_info(:update_metrics, socket) do
  new_metrics = fetch_metrics()
  
  socket = 
    socket
    |> assign(:metrics, new_metrics)
    |> push_event("chart-update", %{
      chart_id: "metrics-chart",
      data: new_metrics
    })
    
  {:noreply, socket}
end
```

### WebSocket Integration

```elixir
# In your channel
def handle_in("subscribe_chart", %{"chart_id" => chart_id}, socket) do
  Phoenix.PubSub.subscribe(MyApp.PubSub, "chart:#{chart_id}")
  {:reply, :ok, socket}
end

# In your LiveView
def handle_info({:chart_data, chart_id, data}, socket) do
  socket = push_event(socket, "chart-update", %{
    chart_id: chart_id,
    data: data
  })
  
  {:noreply, socket}
end
```

## Performance Considerations

### 1. Data Size Optimization

- Limit data points to reasonable numbers (< 100 for smooth performance)
- Use data aggregation for large datasets
- Implement pagination for historical data

### 2. Update Frequency

- Avoid updating charts more than once per second
- Use debouncing for rapid updates
- Consider using `phx-update="ignore"` for static charts

### 3. Memory Management

- The EChart hook automatically handles chart cleanup
- Resize observers are properly disconnected on component destruction
- Charts are destroyed when components unmount

## Troubleshooting

### Common Issues

1. **Chart not rendering**
   - Ensure EChartHook is properly imported in app.js
   - Check that the container has explicit width/height
   - Verify data format is correct

2. **Updates not working**
   - Confirm chart_id matches the component id
   - Check browser console for JavaScript errors
   - Ensure push_event is called correctly

3. **Performance issues**
   - Reduce update frequency
   - Limit data points
   - Use appropriate chart variant for data size

### Debug Mode

Enable debug logging in the EChart hook:

```javascript
// In echart_hook.js, set debug mode
const DEBUG = true;

if (DEBUG) {
  console.log('Chart data:', newData);
  console.log('Chart config:', config);
}
```

### Browser Developer Tools

- Use the Network tab to check for failed asset loads
- Check Console for JavaScript errors
- Use Elements tab to inspect chart DOM structure

## Advanced Examples

### Conditional Chart Rendering

```elixir
<div :if={length(@data) > 0}>
  <PieChart.pie_chart
    id="conditional-chart"
    data={@data}
    loading={@loading}
  />
</div>
<div :if={length(@data) == 0} class="text-center py-8">
  <p class="text-gray-500">No data available</p>
</div>
```

### Multiple Charts with Shared Data

```elixir
<div class="grid grid-cols-2 gap-4">
  <PieChart.pie_chart
    id="chart-1"
    title="Current Period"
    data={@current_data}
    variant="pie"
  />
  <PieChart.pie_chart
    id="chart-2"
    title="Previous Period"
    data={@previous_data}
    variant="donut"
  />
</div>
```

This guide provides a comprehensive foundation for implementing pie charts in your LiveView applications. For additional customization options, refer to the component documentation and ECharts official documentation.
