defmodule Admin.AdminRepo.Migrations.AddDiscountCodesColumnToShopifyOrder do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:shopify_orders, prefix: "metrics") do
      add :discount_codes, :text
    end
  end

  def down do
    alter table(:shopify_orders, prefix: "metrics") do
      remove :discount_codes
    end
  end
end
